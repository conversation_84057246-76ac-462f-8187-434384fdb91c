﻿using Dolo.Bot.Shard;
using Dolo.Bot.Shop.Hub.Interaction;
using Dolo.Bot.Shop.Hub.Ticket;
using Dolo.Bot.Shop.Hub.Ticket.Extension;
using Dolo.Bot.Shop.Hub.Ticket.Interaction;
using DSharpPlus.EventArgs;
namespace Dolo.Bot.Shop.Hub.Handler;

public static class ComponentInteractionCreated
{
    public static Task InvokeAsync(this ComponentInteractionCreatedEventArgs e)
    {
        return e.Id switch
        {
            "pluto-shop-complete" => e.InvokeShopCompleteAsync(),
            "pluto-shop-buy" => e.InvokeBuyPressedAsync(),
            "pluto-shop-option" => e.InvokeShopOptionAsync(),
            "pluto-shop-question" => e.InvokeShopQuestionAsync(),
            "pluto-shop-delete" => e.InvokeShopDeleteAsync(),
            "pluto-shop-agree" => e.InvokeShopAgreeAsync(),
            "pluto-shop-paid" => e.InvokeShopPaidAsync(),
            "suggest-idea" => e.InvokeIdeaAsync(),
            "reward-my-invites" => e.InvokeMyInvitesAsync(),
            "reward-create-invite" or "create-invite" => e.InvokeCreateInviteAsync(HubCache.GetInvites(e.User.Id)),
            "pluto-autograph-preview" => e.Interaction.CreateResponseAsync(DiscordInteractionResponseType.ChannelMessageWithSource, new DiscordInteractionResponseBuilder().WithContent("https://raw.githubusercontent.com/cydolo/assets/refs/heads/main/pluto/autograph.png").AsEphemeral()),
            _ => e.Id.StartsWith("event-") ? e.InvokeEventAsync(Hub.MemberSearchSystem, HubEmoji.IceCube!) : Task.CompletedTask
        };
    }
}