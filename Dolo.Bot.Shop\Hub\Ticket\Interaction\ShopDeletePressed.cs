using Dolo.Bot.Shop.Hub.Ticket.Extension;
using Dolo.Core.Discord;
using Dolo.Database;
using DSharpPlus.EventArgs;
namespace Dolo.Bot.Shop.Hub.Ticket.Interaction;

public static class ShopDeletePressed
{
    public static async Task InvokeShopDeleteAsync(this ComponentInteractionCreatedEventArgs e)
    {
        if(e.User.Id != Shop.Admin)
           return;   
        

        var db = await TicketDatabase.GetTicketAsync(e.Channel.Id);
        if (db is null)
        {
            await e.Channel.TryDeleteAsync();
            return;
        }

        await TicketDatabase.DeleteTicketAsync(e.Channel.Id);
        await e.Channel.TryDeleteAsync();
    }
}