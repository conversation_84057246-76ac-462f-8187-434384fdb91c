﻿using Dolo.Authentication;
using Dolo.Core.Mongo;
using Dolo.Planet.Entities;
using Dolo.Pluto.Shard;
using Dolo.Pluto.Shard.Bot.Apple;
using Dolo.Pluto.Shard.Bot.Pixi;
using Dolo.Pluto.Shard.Bot.Shop;
using Dolo.Pluto.Shard.MovieStarPlanet;
using Dolo.Pluto.Shard.Server;
using Dolo.Pluto.Shard.Toolbox;
namespace Dolo.Database;

/// <summary>
///     The Database class provides static access to various MongoDB collections.
/// </summary>
public static class Mongo
{
    /// <summary>
    ///     The MongoDB host address.
    /// </summary>
    private const string Host = "*************";

    /// <summary>
    ///     The MongoDB port number.
    /// </summary>
    private const int Port = 27017;

    /// <summary>
    ///     The name of the MongoDB database.
    /// </summary>
    private const string DatabaseName = "pluto";

    /// <summary>
    ///     The username for MongoDB authentication.
    /// </summary>
    private const string Username = "bufferly";

    /// <summary>
    ///     The token for MongoDB authentication, retrieved from the Authenticator.
    /// </summary>
    private static string? Token { get; } = Authenticator.GetAuthValue("MongoToken");

    /// <summary>
    ///     Provides access to the 'pluto-server-members' collection in MongoDB.
    /// </summary>
    public static Mongo<ServerMember> ServerMembers { get; private set; } =
        new Mongo<ServerMember>()
            .SetCollection("pluto-server-members")
            .SetDatabase(DatabaseName)
            .SetUsername(Username)
            .SetPassword(Token)
            .SetHost(Host)
            .SetPort(Port)
            .Initialize();

    /// <summary>
    ///     Provides access to the 'pluto-server-voice' collection in MongoDB.
    /// </summary>
    public static Mongo<VoiceUser> Voice { get; private set; } =
        new Mongo<VoiceUser>()
            .SetCollection("pluto-server-voice")
            .SetDatabase(DatabaseName)
            .SetUsername(Username)
            .SetPassword(Token)
            .SetHost(Host)
            .SetPort(Port)
            .Initialize();

    /// <summary>
    ///     Provides access to the 'pluto-server-settings' collection in MongoDB.
    /// </summary>
    public static Mongo<ServerSettings> ServerSettings { get; private set; } =
        new Mongo<ServerSettings>()
            .SetCollection("pluto-server-settings")
            .SetDatabase(DatabaseName)
            .SetUsername(Username)
            .SetPassword(Token)
            .SetHost(Host)
            .SetPort(Port)
            .Initialize();


    /// <summary>
    ///     Provides access to the 'pluto-histogram' collection in MongoDB.
    /// </summary>
    public static Mongo<MspHistogram> Histogram { get; private set; } =
        new Mongo<MspHistogram>()
            .SetCollection("pluto-histogram")
            .SetDatabase(DatabaseName)
            .SetUsername(Username)
            .SetPassword(Token)
            .SetHost(Host)
            .SetPort(Port)
            .Initialize();

    /// <summary>
    ///     Provides access to the 'pluto-pixi-tracker' collection in MongoDB.
    /// </summary>
    public static Mongo<PixiTrackingMember> PixiTracking { get; private set; } =
        new Mongo<PixiTrackingMember>()
            .SetCollection("pluto-pixi-tracker")
            .SetDatabase(DatabaseName)
            .SetUsername(Username)
            .SetPassword(Token)
            .SetHost(Host)
            .SetPort(Port)
            .Initialize();

    /// <summary>
    ///     Provides access to the 'pluto-pixi-members' collection in MongoDB.
    /// </summary>
    public static Mongo<PixiMember> PixiMembers { get; private set; } =
        new Mongo<PixiMember>()
            .SetCollection("pluto-pixi-members")
            .SetDatabase(DatabaseName)
            .SetUsername(Username)
            .SetPassword(Token)
            .SetHost(Host)
            .SetPort(Port)
            .Initialize();

    /// <summary>
    ///     Provides access to the 'pluto-pixi' collection in MongoDB.
    /// </summary>
    public static Mongo<PixiSettings> PixiSettings { get; private set; } =
        new Mongo<PixiSettings>()
            .SetCollection("pluto-pixi")
            .SetDatabase(DatabaseName)
            .SetUsername(Username)
            .SetPassword(Token)
            .SetHost(Host)
            .SetPort(Port)
            .Initialize();

    /// <summary>
    ///     Provides access to the 'pluto-pixi-cloth' collection in MongoDB.
    /// </summary>
    public static Mongo<PixiCloth> PixiCloth { get; private set; } =
        new Mongo<PixiCloth>()
            .SetCollection("pluto-pixi-cloth")
            .SetDatabase(DatabaseName)
            .SetUsername(Username)
            .SetPassword(Token)
            .SetHost(Host)
            .SetPort(Port)
            .Initialize();

    /// <summary>
    ///     Provides access to the 'pluto-histogram' collection in MongoDB.
    /// </summary>
    public static Mongo<ShopTicketMember> ShopTicket { get; private set; } =
        new Mongo<ShopTicketMember>()
            .SetCollection("pluto-shop-ticket")
            .SetDatabase(DatabaseName)
            .SetUsername(Username)
            .SetPassword(Token)
            .SetHost(Host)
            .SetPort(Port)
            .Initialize();


    /// <summary>
    ///     Provides access to the 'pluto-histogram' collection in MongoDB.
    /// </summary>
    public static Mongo<RewardSettings> ShopRewards { get; private set; } =
        new Mongo<RewardSettings>()
            .SetCollection("pluto-shop-rewards")
            .SetDatabase(DatabaseName)
            .SetUsername(Username)
            .SetPassword(Token)
            .SetHost(Host)
            .SetPort(Port)
            .Initialize();


    /// <summary>
    ///     Provides access to the 'pluto-rewards' collection in MongoDB.
    /// </summary>
    public static Mongo<RewardsInfo> Rewards { get; private set; } =
        new Mongo<RewardsInfo>()
            .SetCollection("pluto-rewards")
            .SetDatabase(DatabaseName)
            .SetUsername(Username)
            .SetPassword(Token)
            .SetHost(Host)
            .SetPort(Port)
            .Initialize();

    // create new database for our toolbox 

    /// <summary>
    ///    Provides access to the 'pluto-toolbox' collection in MongoDB.
    ///    </summary>
    public static Mongo<Toolbox> Toolbox { get; private set; } =
        new Mongo<Toolbox>()
            .SetCollection("pluto-toolbox")
            .SetDatabase(DatabaseName)
            .SetUsername(Username)
            .SetPassword(Token)
            .SetHost(Host)
            .SetPort(Port)
            .Initialize();

    /// <summary>
    ///    Provides access to the 'pluto-toolbox' collection in MongoDB.
    ///    </summary>
    public static Mongo<ActorId> ActorId { get; private set; } =
        new Mongo<ActorId>()
            .SetCollection("actorId")
            .SetDatabase("MovieStarPlanet")
            .SetUsername(Username)
            .SetPassword(Token)
            .SetHost(Host)
            .SetPort(Port)
            .Initialize();
}