﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net10.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <OutputType>Library</OutputType>
        <LangVersion>preview</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Dolo\Dolo.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="MethodTimer.Fody" Version="3.2.3" PrivateAssets="All"/>
        <PackageReference Include="NLog" Version="6.0.2"/>
        <PackageReference Include="NLog.Extensions.Logging" Version="6.0.2"/>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="NET\Commands\AMFAnchorCharacterService\"/>
        <Folder Include="NET\Commands\AMFAppSettingsService\"/>
        <Folder Include="NET\Commands\AMFBeautyClinicService\"/>
        <Folder Include="NET\Commands\AMFGenericSnapshotService\"/>
        <Folder Include="NET\Commands\AMFMovieStarService\"/>
        <Folder Include="NET\Commands\AMFPaymentService\"/>
        <Folder Include="NET\Commands\AMFPiggyBankService\"/>
        <Folder Include="NET\Commands\AMFSessionServiceForWeb\"/>
        <Folder Include="NET\Commands\AMFShopContentService\"/>
        <Folder Include="NET\Commands\AMFWorldThemeService\"/>
    </ItemGroup>

</Project>
