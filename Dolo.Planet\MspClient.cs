﻿using System.Diagnostics.CodeAnalysis;
using System.Net.WebSockets;
using Dolo.Core.Extension;
using Dolo.Planet.Entities;
using Dolo.Planet.Entities.Beauty;
using Dolo.Planet.Entities.Cloth;
using Dolo.Planet.Entities.Friend;
using Dolo.Planet.Entities.Game;
using Dolo.Planet.Entities.Movie;
using Dolo.Planet.Entities.Score;
using Dolo.Planet.Entities.Single;
using Dolo.Planet.Entities.Status;
using Dolo.Planet.Entities.Theme;
using Dolo.Planet.Enums;
using Dolo.Planet.NET.Commands;
using Dolo.Planet.NET.Commands.AMFActorService;
using Dolo.Planet.NET.Commands.AMFActorServiceForWeb;
using Dolo.Planet.NET.Commands.AMFAnchorCharacterService;
using Dolo.Planet.NET.Commands.AMFAppSettingsService;
using Dolo.Planet.NET.Commands.AMFBeautyClinicService;
using Dolo.Planet.NET.Commands.AMFBonsterService;
using Dolo.Planet.NET.Commands.AMFCommonWebService;
using Dolo.Planet.NET.Commands.AMFCompetitionService;
using Dolo.Planet.NET.Commands.AMFDailyCompetitionService;
using Dolo.Planet.NET.Commands.AMFFriendshipService;
using Dolo.Planet.NET.Commands.AMFGenericSnapshotService;
using Dolo.Planet.NET.Commands.AMFGiftsService;
using Dolo.Planet.NET.Commands.AMFHighscoreService;
using Dolo.Planet.NET.Commands.AMFImageUpload;
using Dolo.Planet.NET.Commands.AMFLookService;
using Dolo.Planet.NET.Commands.AMFMobileService;
using Dolo.Planet.NET.Commands.AMFModeration;
using Dolo.Planet.NET.Commands.AMFMovieService;
using Dolo.Planet.NET.Commands.AMFMovieStarService;
using Dolo.Planet.NET.Commands.AMFOs;
using Dolo.Planet.NET.Commands.AMFPaymentService;
using Dolo.Planet.NET.Commands.AMFPetService;
using Dolo.Planet.NET.Commands.AMFPiggyBankService;
using Dolo.Planet.NET.Commands.AMFProfileService;
using Dolo.Planet.NET.Commands.AMFScrapBlogService;
using Dolo.Planet.NET.Commands.AMFSessionServiceForWeb;
using Dolo.Planet.NET.Commands.AMFShopContentService;
using Dolo.Planet.NET.Commands.AMFSpendingService;
using Dolo.Planet.NET.Commands.AMFUserServiceWeb;
using Dolo.Planet.NET.Commands.AMFUserSessionService;
using Dolo.Planet.NET.Commands.AMFWorldThemeService;
using Dolo.Planet.NET.Entities;
using Dolo.Planet.Websocket;
using MethodTimer;
using Microsoft.Extensions.Logging;

[assembly: InternalsVisibleTo("Dolo.Pluto.Server")]
[assembly: InternalsVisibleTo("Dolo.Pluto.Maui")]
[assembly: InternalsVisibleTo("Dolo.Bot.Pixi")]
[assembly: InternalsVisibleTo("Dolo.Test")]
[assembly: InternalsVisibleTo("Dolo.Nebula")]
[assembly: InternalsVisibleTo("Dolo.Planet.Histogram")]
[assembly: InternalsVisibleTo("Dolo.Pluto.Tools.Autograph")]
[assembly: InternalsVisibleTo("Dolo.Pluto.Tools.Fame")]
[assembly: InternalsVisibleTo("Pluto")]
[assembly: InternalsVisibleTo("Dolo.Pluto.App")]
[assembly: InternalsVisibleTo("Dolo.Pluto.Tool.Pixler")]
[assembly:
    Obfuscation(Feature = "apply to type Dolo.Planet.Websocket.Entities.*: renaming", Exclude = true,
        ApplyToMembers = true)]
[assembly:
    Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.*: all", Exclude = true, ApplyToMembers = true)]

namespace Dolo.Planet;

[Obfuscation(Feature = "apply to member * when method: renaming", Exclude = true)]
[Obfuscation(Feature = "internalization", Exclude = true)]
[SuppressMessage("ReSharper", "InvalidXmlDocComment")]
/// <summary>
/// The MspClient class is responsible for managing the connection to the MSP server.
/// It provides methods for logging in and out, sending commands, and retrieving data.
/// </summary>
public class MspClient : IAsyncDisposable
{
    /// <summary>
    ///     The API instance used to interact with the MSP server.
    /// </summary>
    internal readonly MspApi Api;

    /// <summary>
    ///     The configuration instance used to store settings for the MSP client.
    /// </summary>
    internal readonly MspConfig Config;

    /// <summary>
    ///     The WebSocket instance used for real-time communication with the MSP server.
    /// </summary>
    internal readonly MspWebSocket Socket;

    /// <summary>
    ///     The logger instance used to log messages.
    /// </summary>
    internal ILoggerFactory LogFactory;

    /// <summary>
    ///     The logger instance used to log messages.
    /// </summary>
    internal ILogger Logger;

    /// <summary>
    ///     Event triggered when the client logs in.
    /// </summary>
    public MspEvent<MspClient, string?>? OnLogin;

    /// <summary>
    ///     The user instance representing the logged-in user.
    /// </summary>
    internal MspLogin User;

    /// <summary>
    ///     Constructor for the MspClient class.
    /// </summary>
    /// <param name="config">A delegate that configures the MspConfig object.</param>
    public MspClient(Action<MspConfig> config)
    {
        AMFAttribute.SetAttributes();

        Config = config.GetAction();

        LogFactory = Config.Logger ??
                     LoggerFactory.Create(builder => builder.AddConsole().AddDebug().SetMinimumLevel(LogLevel.None));
        Logger = LogFactory.CreateLogger<MspClient>();

        Api = new MspApi(this);
        Socket = new MspWebSocket();
        User = new MspLogin();
    }

    /// <summary>
    ///     Checks if the user is logged in.
    /// </summary>
    public bool IsLoggedIn
        => User.LoggedIn;

    /// <summary>
    ///     Dispose the MspClient and log out.
    /// </summary>
    public async ValueTask DisposeAsync()
    {
        await LogOutAsync();
        GC.SuppressFinalize(this);
    }

    /// <summary>
    ///     Logs out the user.
    /// </summary>
    public async Task LogOutAsync()
    {
        if (Socket.State == WebSocketState.Open)
            await Socket.DisconnectAsync()
                .ConfigureAwait(false);

        User.LoggedIn = false;
    }

    /// <summary>
    ///     Retrieves the user's login information.
    /// </summary>
    /// <returns>The user's login information.</returns>
    public MspLogin GetUser()
    {
        return User;
    }

    /// <summary>
    ///     Retrieves the user's access token.
    /// </summary>
    /// <returns>The user's access token.</returns>
    public string? GetAccessToken()
    {
        return User.Actor.Nebula?.AccessToken;
    }

    /// <summary>
    ///     Sends a custom command to MSP.
    /// </summary>
    /// <param name="config">The configuration for the REST request.</param>
    /// <returns>The result of the command.</returns>
    public Task<MspResult<object>> SendAsync(Action<RestConfig> config)
    {
        return Api.SendAsync(config.GetAction());
    }

    /// <summary>
    ///     Sends a custom command to MSP and expects a response of a specific type.
    /// </summary>
    /// <param name="config">The configuration for the REST request.</param>
    /// <returns>The result of the command.</returns>
    public Task<MspResult<T>> SendAsync<T>(Action<RestConfig> config)
    {
        return Api.SendAsync<T>(config.GetAction());
    }

    /// <summary>
    ///     Dumps the names of all users.
    /// </summary>
    /// <returns>A list of all user names.</returns>
    public Task<IEnumerable<MspName>> DumpNamesAsync()
    {
        return Api.DumpNamesAsync();
    }

    /// <summary>
    ///     Retrieves all animations.
    /// </summary>
    /// <returns>A list of all animations.</returns>
    public Task<MspList<MspAnimation>> GetAnimationsAsync()
    {
        return GetAnimationsFromCsv.LoadAsync();
    }

    /// <summary>
    ///     Retrieves all clothes.
    /// </summary>
    /// <returns>A list of all clothes.</returns>
    public Task<MspList<MspCLothV>> GetClothesAsync()
    {
        return GetClothesFromCsv.LoadAsync();
    }


    /// <summary>
    ///     Buys a cloth.
    /// </summary>
    /// <param name="clothes">The clothes to buy.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>The status of the cloth shop after the purchase.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Spending.AMFSpendingService.BuyClothes", IsTicketRequired = true)]
    public Task<MspClothShopStatus> BuyClothAsync(IEnumerable<MspClothShop> clothes,
        Action<CommandConfig>? config = null)
    {
        return Api.BuyClothesAsync(clothes, config?.GetAction());
    }

    /// <summary>
    ///     Retrieves friend requests.
    /// </summary>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>A list of friend requests.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Friendships.AMFFriendshipService.GetPagedProfileTodos",
        IsTicketRequired = true)]
    public Task<MspList<MspTodoActor>> GetFriendRequestsAsync(Action<CommandConfig>? config = null)
    {
        return Api.GetPagedProfileTodosAsync(config?.GetAction());
    }

    /// <summary>
    ///     Searches for an actor by username.
    /// </summary>
    /// <param name="username">The username to search for.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>A list of actors that match the search.</returns>
    [AMFMethod("MovieStarPlanet.WebService.ActorService.AMFActorServiceForWeb.SearchActorByNameNeb",
        IsTicketRequired = true)]
    public Task<MspList<MspSearch>> SearchActorAsync(string? username, Action<CommandConfig>? config = null)
    {
        return Api.SearchActorByNameNebAsync(username, config?.GetAction());
    }

    /// <summary>
    ///     Logs in to the MSP server using the login data from the config file.
    /// </summary>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>The login information.</returns>
    [Time]
    [AMFMethod("MovieStarPlanet.WebService.User.AMFUserServiceWeb.Login")]
    public Task<MspLogin> LoginAsync(Action<CommandConfig>? config = null)
    {
        return Api.LoginAsync(Config.Username, Config.Password, config?.GetAction());
    }

    /// <summary>
    ///     Attempts to log in to the MSP server and returns whether the login was successful.
    /// </summary>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>True if the login was successful, false otherwise.</returns>
    [Time]
    [AMFMethod("MovieStarPlanet.WebService.User.AMFUserServiceWeb.Login")]
    public async Task<bool> TryLoginAsync(Action<CommandConfig>? config = null)
    {
        return await Api.LoginAsync(Config.Username, Config.Password, config?.GetAction()).ConfigureAwait(false) is
            { LoggedIn: true };
    }

    /// <summary>
    ///     Method to login to the msp server
    /// </summary>
    /// <param name="username">the username</param>
    /// <param name="password">the password</param>
    /// <param name="server">the server</param>
    /// <returns></returns>
    [Time]
    public Task<MspLogin> LoginAsync(string? username, string? password, Action<CommandConfig>? config = null)
    {
        return Api.LoginAsync(username, password, config?.GetAction());
    }

    /// <summary>
    ///     Logs in to a non-existing MSP account. This method is used for development purposes.
    /// </summary>
    /// <returns>A mock login object for a non-existing MSP account.</returns>
    public MspLogin LoginFake()
    {
        return new MspLogin(true)
        {
            Success = true,
            Actor = new MspUser().CreateUser()
        };
    }

    /// <summary>
    ///     Checks if a given text is censored or not.
    /// </summary>
    /// <param name="text">The text to check.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the filter result.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Moderation.AMFModeration.FilterText", IsTicketRequired = true)]
    public Task<MspFilter> FilterTextAsync(string text, Action<CommandConfig>? config = null)
    {
        return Api.FilterTextAsync(text, config?.GetAction());
    }

    /// <summary>
    ///     Sends a friend request to a user.
    /// </summary>
    /// <param name="id">The ID of the user to send the friend request to.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains a boolean indicating the success
    ///     of the operation.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.Friendships.AMFFriendshipService.RequestFriendship",
        IsTicketRequired = true)]
    public Task<MspResult<bool>> RequestFriendshipAsync(int id, Action<CommandConfig>? config = null)
    {
        return Api.RequestFriendshipAsync(id, config?.GetAction());
    }

    /// <summary>
    ///     Rejects a friend request from a user.
    /// </summary>
    /// <param name="id">The ID of the user whose friend request to reject.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains an object indicating the result of
    ///     the operation.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.Friendships.AMFFriendshipService.RejectFriendShip", IsTicketRequired = true)]
    public Task<MspResult<object>> RejectFriendshipAsync(int id, Action<CommandConfig>? config = null)
    {
        return Api.RejectFriendShipAsync(id, config?.GetAction());
    }

    /// <summary>
    ///     Retrieves the highscore list for the Bonster game.
    /// </summary>
    /// <param name="option">The options for retrieving the highscore list.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains a list of highscores for the
    ///     Bonster game.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.Highscore.AMFHighscoreService.GetHighscoreBonster", IsTicketRequired = true)]
    public Task<MspList<MspScoreBonster>> GetHighscoreBonsterAsync(Action<MspScoreOption> option,
        Action<CommandConfig>? config = null)
    {
        return Api.GetHighscoreBonsterAsync(option.GetAction(), config?.GetAction());
    }

    /// <summary>
    ///     Retrieves the highscore list for the Pet game.
    /// </summary>
    /// <param name="option">The options for retrieving the highscore list.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains a list of highscores for the Pet
    ///     game.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.Highscore.AMFHighscoreService.GetHighscorePet", IsTicketRequired = true)]
    public Task<MspList<MspScorePet>> GetHighscorePetAsync(Action<MspScoreOption> option,
        Action<CommandConfig>? config = null)
    {
        return Api.GetHighscorePetAsync(option.GetAction(), config?.GetAction());
    }

    /// <summary>
    ///     Retrieves the highscore list for Looks.
    /// </summary>
    /// <param name="option">The options for retrieving the highscore list.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of highscores for Looks.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Highscore.AMFHighscoreService.GetHighscoreLook", IsTicketRequired = true)]
    public Task<MspList<MspScoreLook>> GetHighscoreLooksAsync(Action<MspScoreOption> option,
        Action<CommandConfig>? config = null)
    {
        return Api.GetHighscoreLookAsync(option.GetAction(), config?.GetAction());
    }

    /// <summary>
    ///     Retrieves the highscore list for Movies.
    /// </summary>
    /// <param name="option">The options for retrieving the highscore list.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of highscores for Movies.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Highscore.AMFHighscoreService.GetHighscoreMovie", IsTicketRequired = true)]
    public Task<MspList<MspScoreMovie>> GetHighscoreMoviesAsync(Action<MspScoreOption> option,
        Action<CommandConfig>? config = null)
    {
        return Api.GetHighscoreMovieAsync(option.GetAction(), config?.GetAction());
    }

    /// <summary>
    ///     Retrieves the highscore list for Artbooks.
    /// </summary>
    /// <param name="option">The options for retrieving the highscore list.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of highscores for Artbooks.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Highscore.AMFHighscoreService.GetHighscoreScrapBlog",
        IsTicketRequired = true)]
    public Task<MspList<MspScoreArtbook>> GetHighscoreArtbookAsync(Action<MspScoreOption> option,
        Action<CommandConfig>? config = null)
    {
        return Api.GetHighscoreArtbooksAsync(option.GetAction(), config?.GetAction());
    }

    /// <summary>
    ///     Retrieves the highscore list for Actors.
    /// </summary>
    /// <param name="option">The options for retrieving the highscore list.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of highscores for Actors.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Highscore.AMFHighscoreService.GetHighscoreActor", IsTicketRequired = true)]
    public Task<MspList<MspActor>> GetHighscoreActorsAsync(Action<MspScoreOption> option,
        Action<CommandConfig>? config = null)
    {
        return Api.GetHighscoreActorsAsync(option.GetAction(), config?.GetAction());
    }

    /// <summary>
    ///     Retrieves the list of new picture uploads.
    /// </summary>
    /// <param name="amount">The number of new uploads to retrieve. Default is 500.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of new picture uploads.</returns>
    [AMFMethod("MovieStarPlanet.WebService.ImageUpload.AMFImageUpload.GetNewUploads", IsTicketRequired = true)]
    public Task<MspList<MspPicture>> GetNewPicturesAsync(int amount = 500, Action<CommandConfig>? config = null)
    {
        return Api.GetNewUploadsAsync(amount, config?.GetAction());
    }

    /// <summary>
    ///     Retrieves the list of pictures uploaded by a specific actor.
    /// </summary>
    /// <param name="id">The ID of the actor.</param>
    /// <param name="amount">The number of uploads to retrieve. Default is 500.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains a list of pictures uploaded by the
    ///     actor.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.ImageUpload.AMFImageUpload.GetUploadsFromUser", IsTicketRequired = true)]
    public Task<MspList<MspPicture>> GetActorPictures(int id, int amount = 500, Action<CommandConfig>? config = null)
    {
        return Api.GetUploadsFromUserAsync(id, amount, config?.GetAction());
    }

    /// <summary>
    ///     Retrieves the ID of an actor by their username.
    /// </summary>
    /// <param name="name">The username of the actor.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the ID of the actor.</returns>
    [AMFMethod("MovieStarPlanet.WebService.AMFActorService.GetActorIdByName")]
    public Task<MspId> GetActorIdAsync(string name, Action<CommandConfig>? config = null)
    {
        return Api.GetActorIdByNameAsync(name, config?.GetAction());
    }

    /// <summary>
    ///     Retrieves the username of an actor by their ID.
    /// </summary>
    /// <param name="id">The ID of the actor.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the username of the actor.</returns>
    [AMFMethod("MovieStarPlanet.WebService.UserSession.AMFUserSessionService.GetActorNameFromId")]
    public Task<MspName> GetActorNameAsync(int id, Action<CommandConfig>? config = null)
    {
        return Api.GetActorNameAsync(id, config?.GetAction());
    }

    /// <summary>
    ///     Retrieves an actor by their ID.
    /// </summary>
    /// <param name="id">The ID of the actor.</param>
    /// <param name="infos">Optional information to retrieve about the actor.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the actor.</returns>
    [AMFMethod("MovieStarPlanet.WebService.AMFActorService.BulkLoadActors", IsTicketRequired = true)]
    public Task<MspActor> GetActorAsync(int id, IEnumerable<FetchActorInfo>? infos = default,
        Action<CommandConfig>? config = null)
    {
        return Api.GetActorAsync(infos, "", id, config?.GetAction());
    }

    /// <summary>
    ///     Retrieves an actor by their username.
    /// </summary>
    /// <param name="name">The username of the actor.</param>
    /// <param name="infos">Optional information to retrieve about the actor.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the actor.</returns>
    [AMFMethod("MovieStarPlanet.WebService.AMFActorService.BulkLoadActors", IsTicketRequired = true)]
    public Task<MspActor> GetActorAsync(string name, IEnumerable<FetchActorInfo>? infos = null,
        Action<CommandConfig>? config = null)
    {
        return Api.GetActorAsync(infos ?? [FetchActorInfo.Status, FetchActorInfo.Summary], name, 0,
            config?.GetAction());
    }


    /// <summary>
    ///     Method to get multiple actors with the ids
    /// </summary>
    /// <param name="ids">array of ids</param>
    /// <returns></returns>
    public Task<MspList<MspActor>> GetActorBulksAsync(bool withCreationDate = false, bool withStatus = true,
        bool processUrl = true, params int[] ids)
    {
        return Api.GetActorBulksAsync(withCreationDate: withCreationDate, withStatus: withStatus,
            processUrl: processUrl, ids: ids.ToList(), mth: nameof(GetActorAsync));
    }

    /// <summary>
    ///     Method to send a look to the competition
    /// </summary>
    /// <param name="id">the id of the look</param>
    /// <param name="actorid">the id of the actor</param>
    /// <returns></returns>
    [AMFMethod("MovieStarPlanet.WebService.DailyCompetition.AMFDailyCompetitionService.addToComp",
        IsTicketRequired = true)]
    public Task<MspResult<double>> AddCompetitionAsync(Action<CommandConfig>? config = null)
    {
        return Api.AddCompetitionAsync(config?.GetAction());
    }

    /// <summary>
    ///     Method to send a look to the competition
    /// </summary>
    /// <param name="id">the id of the look</param>
    /// GetAppSettingsAsync
    /// <param name="actorid">the id of the actor</param>
    /// <returns></returns>
    [AMFMethod("MovieStarPlanet.WebService.Session.AMFSessionServiceForWeb.GetChatPermissionInfo",
        IsTicketRequired = true)]
    public Task<MspChatPermission> GetChatPermissionsAsync()
    {
        return Api.GetChatPermissionsAsync();
    }


    /// <summary>
    ///     Creates a snapshot.
    /// </summary>
    /// <param name="snapshot">The snapshot data as a byte array.</param>
    /// <param name="type">The type of the snapshot.</param>
    /// <param name="format">The format of the snapshot. Default is "png".</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains a boolean indicating the success
    ///     of the operation.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.Snapshots.AMFGenericSnapshotService.CreateSnapshot",
        IsTicketRequired = true)]
    public Task<MspResult<bool>> CreateSnapshotAsync(byte[] snapshot, string type, string format = "png")
    {
        return Api.CreateSnapshotAsync(snapshot, type, format);
    }

    /// <summary>
    ///     Retrieves the application settings.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of application settings.</returns>
    [AMFMethod("MovieStarPlanet.WebService.AppSettings.AMFAppSettingsService.GetAppSettings", IsTicketRequired = true)]
    public Task<MspList<MspAppSettings>> GetAppSettingsAsync()
    {
        return Api.GetAppSettingsAsync();
    }

    /// <summary>
    ///     Retrieves all themes.
    /// </summary>
    /// <param name="count">The number of themes to retrieve. Default is 50.</param>
    /// <param name="getUpcomming">A boolean indicating whether to retrieve upcoming themes. Default is false.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of themes.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Shopping.AMFShopContentService.GetThemes", IsTicketRequired = true)]
    public Task<MspThemeContent> GetThemesAsync(int count = 50, bool getUpcomming = false)
    {
        return Api.GetThemesAsync(count, getUpcomming);
    }

    /// <summary>
    ///     Retrieves the piggy bank.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains the piggy bank.</returns>
    [AMFMethod("MovieStarPlanet.WebService.PiggyBank.AMFPiggyBankService.GetPiggyBank", IsTicketRequired = true)]
    public Task<MspPiggy> GetPiggyBankAsync()
    {
        return Api.GetPiggyBankAsync();
    }

    /// <summary>
    ///     Retrieves the user's game status data.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains the user's game status data.</returns>
    [AMFMethod("MovieStarPlanet.WebService.AMFMobileService.GetUserGameStatusData", IsTicketRequired = true)]
    public Task<GameUserData> GetGameUserDataAsync()
    {
        return Api.GetGameUserDataAsync();
    }

    /// <summary>
    ///     Adds game points to the user's account.
    /// </summary>
    /// <param name="data">The data for the game points to add.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains an object indicating the result of
    ///     the operation.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.AMFMobileService.AddUserGamePoints", IsTicketRequired = true)]
    public Task<object?> AddGamePointAsync(GamePointData data)
    {
        return Api.AddGamePointAsync(data);
    }

    /// <summary>
    ///     Requests a game reward.
    /// </summary>
    /// <param name="itemid">The ID of the item for which to request a reward.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the reward data.</returns>
    [AMFMethod("MovieStarPlanet.WebService.AMFMobileService.RequestAward", IsTicketRequired = true)]
    public Task<RequestRewardData> RequestAwardAsync(int itemid)
    {
        return Api.RequestAwardAsync(new GameRewardData(itemid));
    }

    /// <summary>
    ///     Redeems a code.
    /// </summary>
    /// <param name="code">The code to redeem.</param>
    /// <param name="isMagazine">A boolean indicating whether the code is from a magazine. Default is false.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains the result of the redeem
    ///     operation.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.Payment.AMFPaymentService.RedeemCode", IsTicketRequired = true)]
    public Task<MspReedem> RedeemCodeAsync(string code, bool isMagazine)
    {
        return Api.RedeemCodeAsync(code, isMagazine);
    }

    /// <summary>
    ///     Retrieves the world theme information.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains the world theme information.</returns>
    [AMFMethod("MovieStarPlanet.WebService.WorldTheme.AMFWorldThemeService.GetWorldThemeInfo", IsTicketRequired = true)]
    public Task<MspWorldTheme> GetWorldThemeAsync()
    {
        return Api.GetWorldThemeAsync();
    }

    /// <summary>
    ///     Creates an OS reference.
    /// </summary>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the OS reference.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Os.AMFOs.CreateOsRef")]
    public Task<MspOsRef> CreateRefAsync(Action<CommandConfig>? config = null)
    {
        return Api.CreateOsRefAsync(config?.GetAction());
    }

    /// <summary>
    ///     Runs an OS check.
    /// </summary>
    /// <param name="refid">The reference ID for the OS check.</param>
    /// <param name="histogram">Optional histogram data for the OS check.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the result of the OS check.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Os.AMFOs.RunOsCheck")]
    public Task<MspOsRun> RunOsAsync(string refid, string? histogram, Action<CommandConfig>? config = null)
    {
        return Api.RunOsCheckAsync(refid, histogram, config?.GetAction());
    }

    /// <summary>
    ///     Adds a like to an actor.
    /// </summary>
    /// <param name="id">The ID of the like.</param>
    /// <param name="actorid">The ID of the actor to like.</param>
    /// <param name="type">The type of the like.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the like.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Common.AMFCommonWebService.LikeAdd", IsTicketRequired = true)]
    public Task<MspLike> LikeAddAsync(int id, int actorid, LikeType type, Action<CommandConfig>? config = null)
    {
        return Api.LikeAddAsync(id, actorid, type, config?.GetAction());
    }

    /// <summary>
    ///     Sets the status of an actor.
    /// </summary>
    /// <param name="builder">The builder for the status message.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the status update.</returns>
    [AMFMethod("MovieStarPlanet.WebService.ActorService.AMFActorServiceForWeb.SetMoodWithModerationCall",
        IsTicketRequired = true)]
    public Task<MspStatusUpdate> SetStatusAsync(Action<StatusMessageBuilder> builder,
        Action<CommandConfig>? config = null)
    {
        return Api.SetMoodWithModerationCallAsync(builder.GetAction(), config?.GetAction());
    }

    /// <summary>
    ///     Loves a pet Bonster.
    /// </summary>
    /// <param name="id">The ID of the pet Bonster to love.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the result of the operation.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Bonster.AMFBonsterService.PetFriendBonster", IsTicketRequired = true)]
    public Task<MspResult<int>> PetBonsterAsync(int id, Action<CommandConfig>? config = null)
    {
        return Api.PetFriendBonsterAsync(id, config?.GetAction());
    }

    /// <summary>
    ///     Loves a pet friend.
    /// </summary>
    /// <param name="id">The ID of the pet friend to love.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the result of the operation.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Pets.AMFPetService.PetFriendPet", IsTicketRequired = true)]
    public Task<MspResult<int>> PetPetAsync(int id)
    {
        return Api.PetFriendPetAsync(id);
    }

    /// <summary>
    ///     Rates a movie.
    /// </summary>
    /// <param name="movie">The movie to rate.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the rating of the movie.</returns>
    [AMFMethod("MovieStarPlanet.WebService.MovieService.AMFMovieService.RateMovie", IsTicketRequired = true)]
    public Task<MspMovieRated> RateMovieAsync(RateMovieConfig movie, Action<CommandConfig>? config = null)
    {
        return Api.RateMovieAsync(movie, config?.GetAction());
    }

    /// <summary>
    ///     Watches a movie.
    /// </summary>
    /// <param name="id">The ID of the movie to watch.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the result of the watch operation.</returns>
    [AMFMethod("MovieStarPlanet.WebService.MovieService.AMFMovieService.MovieWatched", IsTicketRequired = true)]
    public Task<MspMovieWatched> WatchMovieAsync(int id, Action<CommandConfig>? config = null)
    {
        return Api.WatchMovieAsync(id, config?.GetAction());
    }

    /// <summary>
    ///     Gets the status of an actor.
    /// </summary>
    /// <param name="id">The ID of the actor whose status to get.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the actor's status.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Friendships.AMFFriendshipService.GetMspActorSpecialSummary",
        IsTicketRequired = true)]
    public Task<MspStatus> GetActorStatusAsync(int id)
    {
        return Api.GetMspActorSpecialSummaryAsync(id);
    }

    /// <summary>
    ///     Gets the summary of an actor.
    /// </summary>
    /// <param name="id">The ID of the actor whose summary to get.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the actor's summary.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Profile.AMFProfileService.LoadProfileSummary", IsTicketRequired = true)]
    public Task<MspActorSummary> GetActorSummaryAsync(int id)
    {
        return Api.LoadProfileSummaryAsync(id);
    }

    /// <summary>
    ///     Gets a random vote item.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains the vote item.</returns>
    [AMFMethod("MovieStarPlanet.WebService.DailyCompetition.AMFDailyCompetitionService.getRandomItem",
        IsTicketRequired = true)]
    public Task<MspVote> GetRandomItemAsync()
    {
        return Api.GetRandomItemAsync();
    }

    /// <summary>
    ///     Votes for an item.
    /// </summary>
    /// <param name="vote">The vote for the item.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the result of the vote operation.</returns>
    [AMFMethod("MovieStarPlanet.WebService.DailyCompetition.AMFDailyCompetitionService.voteFor",
        IsTicketRequired = true)]
    public Task<MspResult<object>> VoteForAsync(MspVote vote)
    {
        return Api.VoteForAsync(vote);
    }

    /// <summary>
    ///     Sends an autograph.
    /// </summary>
    /// <param name="id">The ID of the actor to send the autograph to.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the autograph.</returns>
    [AMFMethod("MovieStarPlanet.WebService.UserSession.AMFUserSessionService.GiveAutographAndCalculateTimestamp",
        IsTicketRequired = true)]
    public Task<MspAutograph> SendAutographAsync(int id, Action<CommandConfig>? config = null)
    {
        return Api.GiveAutographAndCalculateTimestampAsync(id, config?.GetAction());
    }

    /// <summary>
    ///     Gets an actor's room.
    /// </summary>
    /// <param name="id">The ID of the actor whose room to get.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the actor's room.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Profile.AMFProfileService.loadActorRoom", IsTicketRequired = true)]
    public Task<MspRoom> GetActorRoomAsync(int id)
    {
        return Api.LoadActorRoomAsync(id);
    }

    /// <summary>
    ///     Gets a list of friends.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of friends.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Friendships.AMFFriendshipService.GetFriendListWithNameAndScore",
        IsTicketRequired = true)]
    public Task<MspList<MspFriend>> GetFriendsAsync(Action<CommandConfig>? config = null)
    {
        return Api.GetFriendsAsync(config?.GetAction());
    }

    /// <summary>
    ///     Gets a list of friends with actor information.
    /// </summary>
    /// <param name="withStatus">A boolean indicating whether to include status information. Default is true.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains a list of friends with actor
    ///     information.
    /// </returns>
    public Task<MspList<MspActor>> GetFriendsWithActorAsync(bool withCreationDate = true, bool withStatus = true,
        Action<CommandConfig>? config = null)
    {
        return Api.GetFriendListWithNameAndScoreAsync(withCreationDate, withStatus, config?.GetAction(),
            nameof(GetFriendsAsync));
    }

    /// <summary>
    ///     Deletes a friend.
    /// </summary>
    /// <param name="actorid">The ID of the friend to delete.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains a boolean indicating the success
    ///     of the operation.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.AMFMobileFriendshipService.DeleteFriendship", IsTicketRequired = true)]
    public Task<MspResult<bool>> DeleteFriendAsync(int actorid, Action<CommandConfig>? config = null)
    {
        return Api.DeleteFriendshipAsync(actorid, config?.GetAction());
    }

    /// <summary>
    ///     Buys a beauty item.
    /// </summary>
    /// <param name="item">The beauty item to buy.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the bought beauty item.</returns>
    [AMFMethod("MovieStarPlanet.WebService.BeautyClinic.AMFBeautyClinicService.BuyManyBeautyClinicItems",
        IsTicketRequired = true)]
    public Task<MspResult<MspBeautyItem>> BuyBeautyClinicAsync(MspBeautyItem item, Action<CommandConfig>? config = null)
    {
        return Api.BuyManyBeautyClinicItemsAsync(item, config?.GetAction());
    }

    /// <summary>
    ///     Buys an animation.
    /// </summary>
    /// <param name="id">The ID of the animation to buy.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the bought animation.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Spending.AMFSpendingService.BuyAnimation", IsTicketRequired = true)]
    public Task<MspAnimationBought> BuyAnimationAsync(int id)
    {
        return Api.BuyAnimationAsync(id);
    }

    /// <summary>
    ///     Gets a list of actor's Boonsters.
    /// </summary>
    /// <param name="id">The ID of the actor whose Boonsters to get.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of the actor's Boonsters.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Pets.AMFPetService.GetClickItemsForActorWithPrice", IsTicketRequired = true)]
    public Task<MspList<MspBoonster>> GetActorBoonstersAsync(int id)
    {
        return Api.GetClickItemsForActorWithPriceAsync(id);
    }

    /// <summary>
    ///     Gets a list of an actor's clothes.
    /// </summary>
    /// <param name="id">The ID of the actor whose clothes to get.</param>
    /// <param name="amount">The number of clothes to get. Default is 1000.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of the actor's clothes.</returns>
    [AMFMethod("MovieStarPlanet.WebService.MovieStar.AMFMovieStarService.GetPagedActorClothesByCategories",
        IsTicketRequired = false)]
    public Task<MspList<MspActorCloth>> GetActorClothAsync(int id, int amount = 1000)
    {
        return Api.GetPagedActorClothesByCategoriesAsync(id, amount);
    }

    /// <summary>
    ///     Gets a list of clothes by their IDs.
    /// </summary>
    /// <param name="cloths">The IDs of the clothes to get.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of clothes.</returns>
    [AMFMethod("MovieStarPlanet.WebService.MovieStar.AMFMovieStarService.LoadClothesByIds", IsTicketRequired = false)]
    public Task<MspList<MspCloth>> LoadClothesByIdsAsync(params int[] cloths)
    {
        return Api.LoadClothesByIdsAsync(cloths);
    }

    /// <summary>
    ///     Gets a list of an actor's items.
    /// </summary>
    /// <param name="id">Optional ID of the actor whose items to get. If not provided, gets the items of the current actor.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of the actor's items.</returns>
    [AMFMethod("MovieStarPlanet.WebService.MovieStar.AMFMovieStarService.LoadActorItems", IsTicketRequired = false)]
    public Task<MspList<MspActorCloth>> GetActorItemsAsync(int? id = null, Action<CommandConfig>? config = null)
    {
        return Api.LoadActorItemsAsync(id, config?.GetAction());
    }


    /// <summary>
    ///     Updates the clothes of the current actor.
    /// </summary>
    /// <param name="id">The IDs of the clothes to update.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains the result of the update
    ///     operation.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.MovieStar.AMFMovieStarService.UpdateClothes", IsTicketRequired = true)]
    public Task<MspResult<object>> UpdateClothesAsync(params int[] id)
    {
        return Api.UpdateClothesAsync(id);
    }

    /// <summary>
    ///     Wears items for the current actor.
    /// </summary>
    /// <param name="id">The IDs of the items to wear.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the result of the wear operation.</returns>
    [AMFMethod("MovieStarPlanet.WebService.BeautyClinic.AMFBeautyClinicService.WearItems", IsTicketRequired = true)]
    public Task<MspService> WearItemsAsync(params int[] id)
    {
        return Api.WearItemsAsync(id);
    }

    /// <summary>
    ///     Recycles an item for the current actor.
    /// </summary>
    /// <param name="id">The ID of the item to recycle.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains the result of the recycle
    ///     operation.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.Profile.AMFProfileService.RecycleItem", IsTicketRequired = true)]
    public Task<MspResult<int>> RecycleItemAsync(int id, Action<CommandConfig>? config = null)
    {
        return Api.RecycleItemAsync(id, config?.GetAction());
    }

    /// <summary>
    ///     Retrieves the beauty clinic items for the current actor.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains the beauty clinic items.</returns>
    [AMFMethod("MovieStarPlanet.WebService.BeautyClinic.AMFBeautyClinicService.GetMyBeautyClinicItemsWithHiddenOption",
        IsTicketRequired = true)]
    public Task<MspBeautyData> GetBeautyClinicAsync()
    {
        return Api.GetMyBeautyClinicItemsWithHiddenOptionAsync();
    }

    /// <summary>
    ///     Gives a gift to an actor.
    /// </summary>
    /// <param name="actor">The ID of the actor to give the gift to.</param>
    /// <param name="item">The ID of the item to gift.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the result of the gift operation.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Gifts.AMFGiftsService+Version2.GiveGiftOfCategory", IsTicketRequired = true)]
    public Task<MspGift> GiveGiftAsync(int actor, ulong item, Action<CommandConfig>? config = null)
    {
        return Api.GiveGiftOfCategoryAsync(actor, item, config?.GetAction());
    }

    /// <summary>
    ///     Opens a gift for the current actor.
    /// </summary>
    /// <param name="receiverId">The ID of the receiver.</param>
    /// <param name="giftId">The ID of the gift to open.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the opened gift.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Gifts.AMFGiftsService+Version2.OpenGift", IsTicketRequired = true)]
    public Task<MspResult<object>> OpenGiftAsync(int receiverId, int giftId, Action<CommandConfig>? config = null) {
        return Api.OpenGiftAsync(receiverId, giftId, config?.GetAction());
    }

    /// <summary>
    ///     Checks if the current actor has reached the daily gifts limit.
    /// </summary>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains a boolean indicating whether the
    ///     limit has been reached.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.Gifts.AMFGiftsService+Version2.HasReachedDailyGiftsLimit",
        IsTicketRequired = true)]
    public Task<MspResult<bool>> HasGiftLimitAsync()
    {
        return Api.HasReachedDailyGiftsLimitAsync();
    }

    /// <summary>
    ///     Retrieves the clothes items for an actor.
    /// </summary>
    /// <param name="actor">
    ///     The ID of the actor to retrieve the items for. If not provided, retrieves the items for
    ///     the current actor.
    /// </param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of giftable items.</returns>
    [AMFMethod("MovieStarPlanet.WebService.MovieStar.AMFMovieStarService.LoadPagedActorClothes", IsTicketRequired = true)]
    public Task<MspList<MspClothRel>> LoadPagedActorClothesAsync(int actor = 0) {
        return Api.LoadPagedActorClothesAsync(actor);
    }
        /// <summary>
    ///     Retrieves the giftable clothes for an actor.
    /// </summary>
    /// <param name="actor">
    ///     The ID of the actor to retrieve the giftable items for. If not provided, retrieves the items for
    ///     the current actor.
    /// </param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of giftable items.</returns>
    [AMFMethod("MovieStarPlanet.WebService.MovieStar.AMFMovieStarService.LoadPagedActorGiftableClothes", IsTicketRequired = true)]
    public Task<MspList<MspClothRel>> LoadPagedActorGiftableClothesAsync(int actor = 0) {
        return Api.LoadPagedActorGiftableClothesAsync(actor);
    }

    /// <summary>
    ///     Retrieves the giftable items for an actor.
    /// </summary>
    /// <param name="actor">
    ///     The ID of the actor to retrieve the giftable items for. If not provided, retrieves the items for
    ///     the current actor.
    /// </param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of giftable items.</returns>
    [AMFMethod("MovieStarPlanet.WebService.MovieStar.AMFMovieStarService.LoadPagedActorGiftableItems", IsTicketRequired = true)]
    public Task<MspList<MspClothRel>> LoadPagedActorGiftableItems(int actor = 0) {
        return Api.LoadPagedActorGiftableItemsAsync(actor);
    }

    /// <summary>
    ///     Retrieves a list of sponsors.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of sponsors.</returns>
    [AMFMethod("MovieStarPlanet.WebService.AnchorCharacter.AMFAnchorCharacterService.GetAnchorCharacterList")]
    public Task<MspList<MspSponsor>> GetAnchorCharacterListAsync()
    {
        return Api.GetAnchorCharacterListAsync();
    }

    /// <summary>
    ///     Accepts a friendship request from a sponsor.
    /// </summary>
    /// <param name="id">The ID of the sponsor whose friendship request to accept.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains a boolean indicating the success
    ///     of the operation.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.AnchorCharacter.AMFAnchorCharacterService.AcceptFriendship",
        IsTicketRequired = true)]
    public Task<MspResult<bool>> AcceptFriendshipAsync(int id)
    {
        return Api.AcceptFriendshipAsync(id);
    }

    /// <summary>
    ///     Retrieves the wall posts for an actor.
    /// </summary>
    /// <param name="actorid">
    ///     The ID of the actor to retrieve the wall posts for. If not provided, retrieves the posts for the
    ///     current actor.
    /// </param>
    /// <param name="count">The number of wall posts to retrieve. Default is 100.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of wall posts.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Profile.AMFProfileService.GetWallPosts", IsTicketRequired = true)]
    public Task<MspList<MspWallPost>> GetWallPostsAsync(int actorid = 0, int count = 100)
    {
        return Api.GetWallPostsAsync(actorid, count);
    }

    /// <summary>
    ///     Retrieves the wall activities for an actor.
    /// </summary>
    /// <param name="actorid">
    ///     The ID of the actor to retrieve the wall activities for. If not provided, retrieves the
    ///     activities for the current actor.
    /// </param>
    /// <param name="count">The number of wall activities to retrieve. Default is 100.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of wall activities.</returns>
    [AMFMethod("MovieStarPlanet.WebService.ActorService.AMFActorServiceForWeb.getWallActivitiesForActor",
        IsTicketRequired = true)]
    public Task<MspList<MspWallPostActivities>> GetWallActivitiesAsync(int actorid = 0, int count = 100)
    {
        return Api.GetWallActivitiesForActorAsync(actorid, count);
    }

    /// <summary>
    ///     Retrieves the looks for an actor.
    /// </summary>
    /// <param name="actorid">
    ///     The ID of the actor to retrieve the looks for. If not provided, retrieves the looks for the
    ///     current actor.
    /// </param>
    /// <param name="count">The number of looks to retrieve. Default is 100.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of looks.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Looks.AMFLookService.GetLooksForActor", IsTicketRequired = true)]
    public Task<MspList<MspLook>> GetLooksAsync(int actorid = 0, int count = 100)
    {
        return Api.GetLooksForActorAsync(actorid, count);
    }

    /// <summary>
    ///     Deletes a wall post.
    /// </summary>
    /// <param name="id">The ID of the wall post to delete.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Profile.AMFProfileService.DeleteWallPost", IsTicketRequired = true)]
    public Task<bool> DeleteWallPostAsync(int id, Action<CommandConfig>? config = null)
    {
        return Api.DeleteWallPostAsync(id, config?.GetAction());
    }

    /// <summary>
    ///     Deletes a look.
    /// </summary>
    /// <param name="id">The ID of the look to delete.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Looks.AMFLookService.LookDelete", IsTicketRequired = true)]
    public Task DeleteLookAsync(int id, Action<CommandConfig>? config = null)
    {
        return Api.LookDeleteAsync(id, config?.GetAction());
    }

    /// <summary>
    ///     Deletes an artbook.
    /// </summary>
    /// <param name="id">The ID of the artbook to delete.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    [AMFMethod("MovieStarPlanet.WebService.ScrapBlog.AMFScrapBlogService.DeleteScrapBlog", IsTicketRequired = true)]
    public Task DeleteArtbookAsync(int id, Action<CommandConfig>? config = null)
    {
        return Api.DeleteScrapBlogAsync(id, config?.GetAction());
    }

    /// <summary>
    ///     Retrieves the artbooks for an actor.
    /// </summary>
    /// <param name="actorid">
    ///     The ID of the actor to retrieve the artbooks for. If not provided, retrieves the artbooks for the
    ///     current actor.
    /// </param>
    /// <param name="count">The number of artbooks to retrieve. Default is 5000.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of artbooks.</returns>
    [AMFMethod("MovieStarPlanet.WebService.ScrapBlog.AMFScrapBlogService.GetScrapBlogsByUser", IsTicketRequired = true)]
    public Task<MspList<MspArtbook>> GetArtbooksAsync(int actorid = 0, int count = 5000)
    {
        return Api.GetScrapBlogsByUserAsync(actorid, count);
    }

    /// <summary>
    ///     Posts a message to an actor's guestbook.
    /// </summary>
    /// <param name="message">The message to post.</param>
    /// <param name="actorid">The ID of the actor to post the message to.</param>
    /// <param name="color">The color of the status.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains a boolean indicating the success
    ///     of the operation.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.Profile.AMFProfileService.PostToWallWithModerationCall",
        IsTicketRequired = true)]
    public Task<MspResult<bool>> PostGuestbookAsync(string? message, int actorid, StatusColor color,
        Action<CommandConfig>? config = null)
    {
        return Api.PostToWallWithModerationCallAsync(message, actorid, color, config?.GetAction());
    }

    /// <summary>
    ///     Checks if the current actor has reached the daily gifts limit.
    /// </summary>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains a boolean indicating whether the
    ///     limit has been reached.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.Gifts.AMFGiftsService+Version2.HasReachedDailyGiftsLimit",
        IsTicketRequired = true)]
    public Task<MspResult<bool>> HasReachedDailyGiftsLimitAsync(Action<CommandConfig>? config = null)
    {
        return Api.HasReachedDailyGiftsLimitAsync(config?.GetAction());
    }

    /// <summary>
    ///     Saves the avatar of a third party.
    /// </summary>
    /// <param name="thirdParty">The third party whose avatar to save. If not provided, saves the avatar of the current actor.</param>
    /// <param name="avatarSmall">The small version of the avatar as a byte array.</param>
    /// <param name="avatarBig">The big version of the avatar as a byte array.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains a boolean indicating the success
    ///     of the operation.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.AMFActorService.ThirdPartySaveAvatar")]
    public Task<MspResult<bool>> ThirdPartySaveAsync(MspThirdParty? thirdParty = null, byte[]? avatarSmall = null,
        byte[]? avatarBig = null)
    {
        return Api.ThirdPartySaveAsync(thirdParty ?? new MspThirdParty(Config.Username, Config.Password), avatarSmall,
            avatarBig);
    }

    /// <summary>
    ///     Retrieves the list of actors blocked by the current actor and the list of actors blocking the current actor.
    /// </summary>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains the list of blocked and blocking
    ///     actors.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.ActorService.AMFActorServiceForWeb.LoadBlockedAndBlockingActors",
        IsTicketRequired = true)]
    public Task<MspBlock> GetBlockedListAsync(Action<CommandConfig>? config = null)
    {
        return Api.LoadBlockedAndBlockingActorsAsync(config?.GetAction());
    }

    /// <summary>
    ///     Retrieves the list of movies for an actor.
    /// </summary>
    /// <param name="actorid">
    ///     The ID of the actor to retrieve the movies for. If not provided, retrieves the movies for the
    ///     current actor.
    /// </param>
    /// <param name="count">The number of movies to retrieve. Default is 500.</param>
    /// <param name="config">Optional configuration for the command.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of movies.</returns>
    [AMFMethod("MovieStarPlanet.WebService.MovieService.AMFMovieService.GetMovieListForActor", IsTicketRequired = true)]
    public Task<MspList<MspMovie>> GetMoviesAsync(int? actorid = null, int count = 500, CommandConfig? config = null)
    {
        return Api.GetMovieListForActorAsync(actorid, count, config);
    }

    /// <summary>
    ///     Deletes a movie.
    /// </summary>
    /// <param name="id">The ID of the movie to delete.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains a boolean indicating the success
    ///     of the operation.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.MovieService.AMFMovieService.DeleteMovie", IsTicketRequired = true)]
    public Task<MspResult<bool>> DeleteMovieAsync(int id, Action<CommandConfig>? config = null)
    {
        return Api.DeleteMovieAsync(id, config?.GetAction());
    }

    /// <summary>
    ///     Retrieves the list of face parts for the current actor.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of face parts.</returns>
    [AMFMethod("MovieStarPlanet.WebService.MovieStar.AMFMovieStarService.LoadFaceParts")]
    public Task<MspList<MspFacePart>> LoadFacePartsAsync()
    {
        return Api.LoadFacePartsAsync();
    }

    /// <summary>
    ///     Submits an entity to a competition.
    /// </summary>
    /// <param name="competitionId">The ID of the competition to submit the entity to.</param>
    /// <param name="entityId">The ID of the entity to submit.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the ID of the submitted entity.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Competition.AMFCompetitionService.SubmitEntityToCompetition",
        IsTicketRequired = true)]
    public Task<MspResult<int>> SubmitCompetitionAsync(int competitionId, int entityId)
    {
        return Api.SubmitEntityToCompetitionAsync(competitionId, entityId);
    }

    /// <summary>
    ///     Retrieves the extended details of the current actor.
    /// </summary>
    /// <returns>A task that represents the asynchronous operation. The task result contains the extended details of the actor.</returns>
    [AMFMethod("MovieStarPlanet.WebService.AMFActorService.LoadActorDetailsExtended", IsTicketRequired = true)]
    public Task<MspActorDetailsExtended> LoadActorDetailsExtendedAsync()
    {
        return Api.LoadActorDetailsExtendedAsync();
    }

    /// <summary>
    ///     Retrieves the list of active special items for an actor.
    /// </summary>
    /// <param name="actorId">
    ///     The ID of the actor to retrieve the special items for. If not provided, retrieves the items for
    ///     the current actor.
    /// </param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a list of special items.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Spending.AMFSpendingService.GetActiveSpecialsItems",
        IsTicketRequired = true)]
    public Task<MspList<MspSpecialItems>> GetSpecialItemsAsync(int? actorId = null)
    {
        return Api.GetActiveSpecialsItemsAsync();
    }

    /// <summary>
    ///     Buys a fame booster for the current actor.
    /// </summary>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains a boolean indicating the success
    ///     of the operation.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.Spending.AMFSpendingService.BuyFameBooster", IsTicketRequired = true)]
    public Task<MspServiceResult> BuyFameBoostAsync()
    {
        return Api.BuyFameBoostAsync();
    }

    /// <summary>
    ///     Destroys the piggy bank of the current actor.
    /// </summary>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains the result of the destroy
    ///     operation.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.PiggyBank.AMFPiggyBankService.DestroyPiggyBank", IsTicketRequired = true)]
    public Task<MspServiceResult> DestroyPiggyBankAsync()
    {
        return Api.DestroyPiggyBankAsync();
    }

    /// <summary>
    ///     Checks if the current actor can destroy their piggy bank.
    /// </summary>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains a boolean indicating whether the
    ///     piggy bank can be destroyed.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.PiggyBank.AMFPiggyBankService.CanDestroyPiggyBank", IsTicketRequired = true)]
    public Task<MspServiceResultData<bool>> CanDestroyPiggyBankAsync()
    {
        return Api.CanDestroyPiggyBankAsync();
    }

    /// <summary>
    ///     Buys a special greeting for an actor.
    /// </summary>
    /// <param name="actorId">The ID of the actor to buy the greeting for.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the bought greeting.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Spending.AMFSpendingService.BuySpecialGreeting", IsTicketRequired = true)]
    public Task<MspServiceResultData<MspGreeting>> BuyGreetingAsync(int actorId)
    {
        return Api.BuyGreetingAsync(actorId);
    }

    /// <summary>
    ///     Submits a mobile startup reward.
    /// </summary>
    /// <param name="itemId">The ID of the item to submit as a reward.</param>
    /// <param name="isTop">A boolean indicating whether the item is a top item.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains the result of the submit
    ///     operation.
    /// </returns>
    [AMFMethod("MovieStarPlanet.WebService.AMFActorService.SubmitMobileStartupReward", IsTicketRequired = true)]
    public Task<MspServiceResultData<object>> SubmitMobileStartupRewardAsync(int itemId, bool isTop)
    {
        return Api.SubmitMobileStartupRewardAsync(itemId, isTop);
    }

    /// <summary>
    ///     Adds an item to the wishlist of the current actor.
    /// </summary>
    /// <param name="items">The item to add to the wishlist.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the ID of the added item.</returns>
    [AMFMethod("MovieStarPlanet.WebService.Gifts.AMFGiftsService+Version2.AddItemToWishlist", IsTicketRequired = true)]
    public Task<MspResult<int>> AddItemToWishlistAsync(WishlistItem items)
    {
        return Api.AddItemToWishlistAsync(items);
    }
}