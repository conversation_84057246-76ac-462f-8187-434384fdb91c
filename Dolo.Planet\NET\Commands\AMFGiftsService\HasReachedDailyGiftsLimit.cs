﻿using Dolo.Planet.Entities;
using Dolo.Planet.NET.Entities;
namespace Dolo.Planet.NET.Commands.AMFGiftsService;

/// <summary>
/// HasReachedDailyGiftsLimit class contains methods related to the HasReachedDailyGiftsLimit functionality.
/// </summary>
internal static class HasReachedDailyGiftsLimit
{
    /// <summary>
    /// Checks if the daily gifts limit has been reached asynchronously.
    /// </summary>
    /// <param name="api">The MspApi instance to use for sending the request.</param>
    /// <param name="config">Optional CommandConfig for setting up the request.</param>
    /// <param name="mthd">The method name, automatically filled by the CallerMemberName attribute.</param>
    /// <returns>A Task that represents the asynchronous operation. The task result is a MspResult containing the result of the operation.</returns>
    public static async Task<MspResult<bool>> HasReachedDailyGiftsLimitAsync(
        this MspApi api,
        CommandConfig? config = null,
        [CallerMemberName] string mthd = "")
    {
        // Create a new RestConfig instance and set up the request.
        var req = await api.SendAsync<bool>(mthd, new RestConfig()
                      .SetCancellationToken(config?.CancellationToken)
                      .SetProxy(config?.Proxy));

        // Return a new MspResult with the response data.
        return new()
        {
            MovieStarPlanet = api._client,
            HttpException = req.Exception,
            Success = req.Success,
            HttpRequest = req.Request,
            HttpResponse = req.Response,

            Value = req.Result
        };
    }
}