﻿using Dolo.Core;
using Dolo.Pluto.Shard.Services;
using Microsoft.JSInterop;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class AudioService(IJSRuntime jsRuntime) : IService
{
    public async Task PlayAsync(string url)
    {
        await jsRuntime.TryInvokeVoidAsync("playAudio", url);
    }

    public async Task PlayCongratulationAsync()
    {
        await jsRuntime.TryInvokeVoidAsync("window.sounds.congratulations.play");
    }

    public async Task PlayButtonHoverAsync()
    {
        await jsRuntime.TryInvokeVoidAsync("window.sounds.buttonHover.play");
    }

    public async Task PlayButtonClickAsync()
    {
        await jsRuntime.TryInvokeVoidAsync("window.sounds.buttonClick.play");
    }
}