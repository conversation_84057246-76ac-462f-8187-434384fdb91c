﻿using Dolo.Core;
using Dolo.Core.Extension;
using Dolo.Pluto.App.Components.Content.Component;
using Dolo.Pluto.Components.UI.Dialog;
using Dolo.Pluto.Components.UI.Modal;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;
using Microsoft.JSInterop;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class DialogService(IJSRuntime jsRuntime) : IService
{
    public ExceptionLog? ExceptionLog { get; set; }
    public MSPDetails? Details { get; set; }
    public MSPShop? Shop { get; set; }
    public MSPPiggy? PiggyBank { get; set; }
    public DiscordDialog? DiscordDialog { get; set; }
    public SafetyCheck? Safety { get; set; }
    public Toast? Toast { get; set; }
    public Modal? Loader { get; set; }
    public Dialog? Modal { get; set; }
    public MSPPopup? Popup { get; set; }
    public MSPUserPopup? UserPopup { get; set; }
    public Shop_Animation? AnimationShop { get; set; }
    public Shop_Beauty? BeautyShop { get; set; }

    /// <summary>
    ///     Show a toast message
    /// </summary>
    public async Task ShowToastAsync(string message, bool fromAnotherTask, ToastType type = ToastType.Info)
    {
        await Toast!.AddToastAsync(message, type, fromAnotherTask);
    }

    /// <summary>
    ///     Show a toast message
    /// </summary>
    public async Task ShowToastAsync(string message, ToastType type = ToastType.Info, int duration = 5)
    {
        await Toast!.AddToastAsync(message, type, duration);
    }

    /// <summary>
    ///     Show a toast message
    /// </summary>
    public async Task ShowToastAsync(ToastContent content)
    {
        await Toast!.AddToastAsync(content);
    }

    /// <summary>
    ///     Show a toast message
    /// </summary>
    public async Task ShowToastAsync(Action<ToastContent> content)
    {
        await Toast!.AddToastAsync(content.GetAction());
    }

    /// <summary>
    ///     Show a popup
    /// </summary>
    public async Task ShowPopupAsync(Action<MspPopupOptions> options)
    {
        await Popup!.ShowPopupAsync(options);
    }

    /// <summary>
    ///     Show a Modal
    /// </summary>
    public void ShowPopup(string text, Func<Task> onOk)
    {
        Modal?.Show(text, onOk);
    }

    /// <summary>
    ///     Show a Safety Check Modal
    /// </summary>
    public void ShowSafetyCheck()
    {
        Safety?.Show();
    }

    /// <summary>
    ///     Shows the Animation Shop
    /// </summary>
    public void ShowAnimationShop()
    {
        AnimationShop?.Show();
    }

    /// <summary>
    ///     Shows the Beauty Shop
    /// </summary>
    public void ShowBeautyShop()
    {
        BeautyShop?.Show();
    }

    /// <summary>
    ///     Shows the Shop
    /// </summary>
    public void ShowShop()
    {
        Shop?.Show();
    }

    /// <summary>
    ///     Shows the Shop
    /// </summary>
    public void ShowPiggy()
    {
        PiggyBank?.Show();
    }

    /// <summary>
    ///     Shows the Discord dialog
    /// </summary>
    public void ShowDiscordDialog()
    {
        DiscordDialog?.Show();
    }

    /// <summary>
    ///     Shows the Exception Log
    /// </summary>
    public void ShowExceptionLog(ExceptionInfo info)
    {
        ExceptionLog?.Show(info);
    }

    /// <summary>
    ///     Shows the Details
    /// </summary>
    public void ShowDetails()
    {
        Details?.Show();
    }


    /// <summary>
    ///     Show a user popup
    /// </summary>
    /// <param name="id"></param>
    public async Task ShowUserPopupAsync(int id)
    {
        await UserPopup!.ShowAsync(id);
    }


    /// <summary>
    ///     Shows the loader
    /// </summary>
    public async Task ShowLoaderAsync()
    {
        Loader!.SetVisible(true);
        await jsRuntime.TryInvokeVoidAsync("SetVisibility", "loaderCanvas", true);
    }

    /// <summary>
    ///     Hide the loader
    /// </summary>
    public async Task HideLoaderAsync()
    {
        Loader!.SetVisible(false);
        await jsRuntime.TryInvokeVoidAsync("SetVisibility", "loaderCanvas", false);
    }
}