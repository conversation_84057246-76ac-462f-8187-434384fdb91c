﻿using System.Diagnostics;
using Dolo.Core.Extension;
using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.App.Components.Content.Services;
using Dolo.Pluto.Shard.License;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class FarmService : IService
{
    private static FarmService? _farmService;
    private readonly TimeSpan _baseDelay = TimeSpan.FromSeconds(1);
    private readonly DialogService _dialogService;
    private readonly LoginService _loginService;
    private readonly int _maxConsecutiveFailures = 5;
    private readonly PermissionService _permissionService;
    private readonly TimeSpan _rateLimitCooldown = TimeSpan.FromMinutes(1);
    private readonly StatsService _statsService;
    private readonly TrialService _trialService;
    private int _consecutiveFailures;
    private Timer? _cooldownTimer;
    private TimeSpan _currentRateLimitDuration = TimeSpan.Zero;

    private bool _isStartingFarm;
    private DateTime _lastRateLimit = DateTime.MinValue;
    private DateTime _nextPetTime = DateTime.MinValue;

    public Stopwatch? Stopwatch;

    public FarmService(DialogService dialogService, LoginService loginService, PermissionService permissionService,
        StatsService statsService, TrialService trialService)
    {
        _dialogService = dialogService;
        _loginService = loginService;
        _permissionService = permissionService;
        _statsService = statsService;
        _trialService = trialService;
        _farmService = this;
    }


    public Farm? Farm { get; set; }
    public FarmTrialDialog? TrialDialog { get; set; }
    private CancellationTokenSource? CoinTokenSource { get; set; }
    private Dictionary<int, DateTime> CachedPet { get; } = new();
    public List<FarmEntity> FarmEntities { get; } = new();

    public int CoinEarned { get; private set; }

    public int CoinFailed { get; private set; }

    public bool CoinRunning { get; private set; }

    public bool CoinStarting { get; private set; }

    /// <summary>
    ///     Gets the remaining cooldown time until next pet processing
    /// </summary>
    public TimeSpan NextPetCooldown
    {
        get
        {
            if (!CoinRunning || _nextPetTime == DateTime.MinValue)
                return TimeSpan.Zero;

            var remaining = _nextPetTime - DateTime.Now;
            return remaining > TimeSpan.Zero ? remaining : TimeSpan.Zero;
        }
    }

    /// <summary>
    ///     Gets if we're currently rate limited
    /// </summary>
    public bool IsRateLimited => DateTime.Now - _lastRateLimit < _currentRateLimitDuration;

    /// <summary>
    ///     Gets the current delay reason for display
    /// </summary>
    public string DelayReason
    {
        get
        {
            if (!CoinRunning) return "";

            if (IsRateLimited)
                return $"Rate limited - {NextPetCooldown:mm\\:ss} remaining";

            if (_consecutiveFailures > 0 && NextPetCooldown > TimeSpan.Zero)
                return $"Backoff delay - {NextPetCooldown:mm\\:ss} remaining";

            if (NextPetCooldown > TimeSpan.Zero)
                return $"Normal delay - {NextPetCooldown:mm\\:ss} remaining";

            return "Processing...";
        }
    }

    /// <summary>
    ///     Stop the coin farm with proper cleanup
    /// </summary>
    private void StopCoin()
    {
        Stopwatch?.Stop();
        CoinTokenSource?.Cancel();
        _cooldownTimer?.Dispose();
        _cooldownTimer = null;
        CoinRunning = false;
        CoinStarting = false;
        _consecutiveFailures = 0; // Reset failure counter
        _nextPetTime = DateTime.MinValue; // Reset cooldown
        _currentRateLimitDuration = TimeSpan.Zero; // Reset rate limit duration
        Farm?.StateHasChangedAsync();
    }

    /// <summary>
    ///     Enhanced pet filtering algorithm for optimal farming
    /// </summary>
    private IEnumerable<T> GetOptimalPets<T>(IEnumerable<T> pets) where T : class
    {
        // Use reflection to get pet properties for filtering
        var petType = typeof(T);
        var levelProperty = petType.GetProperty("Level");
        var idProperty = petType.GetProperty("Id");

        if (levelProperty == null || idProperty == null)
            return pets;
        return pets
            .Where(pet =>
            {
                var level = (int?)levelProperty.GetValue(pet);
                var id = (int?)idProperty.GetValue(pet);

                if (level < 40 || id == null) return false;

                // Check if pet is in cooldown
                if (CachedPet.TryGetValue(id.Value, out var lastLoved))
                    return DateTime.Now.Subtract(lastLoved).TotalHours >= 2;

                return true;
            })
            .OrderByDescending(pet => levelProperty.GetValue(pet)) // Prioritize higher level pets
            .ToList()
            .Shuffle(); // Randomize to avoid patterns
    }

    /// <summary>
    ///     Starts the cooldown timer to update UI in real-time
    /// </summary>
    private void StartCooldownTimer()
    {
        _cooldownTimer?.Dispose();
        _cooldownTimer = new Timer(async _ =>
        {
            try
            {
                await Farm?.StateHasChangedAsync()!;
            }
            catch
            {
                // Ignore timer callback errors
            }
        }, null, TimeSpan.Zero, TimeSpan.FromMilliseconds(500)); // Update every 500ms
    }

    /// <summary>
    ///     Sets the next pet processing time and updates the cooldown
    /// </summary>
    private void SetNextPetTime(TimeSpan delay)
    {
        _nextPetTime = DateTime.Now.Add(delay);
    }


    /// <summary>
    ///     Start the starcoins farm
    /// </summary>
    public async Task StartCoinAsync()
    {
        Console.WriteLine("=== StartCoinAsync called ===");

        // If farming is already running, stop it immediately
        if (CoinRunning)
        {
            Console.WriteLine("CoinRunning is true - stopping current farm");
            StopCoin();
            FarmEntities.Prepending(new FarmEntity
            {
                Time = DateTime.Now,
                IsDefaultMessage = true,
                DefaultMessage = "Farming stopped."
            });
            Farm?.StateHasChangedAsync();
            Console.WriteLine("=== Farming stopped successfully ===");
            return;
        }

        // Prevent multiple simultaneous calls for starting
        if (_isStartingFarm)
        {
            Console.WriteLine("=== StartCoinAsync already in progress, ignoring call ===");
            return;
        }

        // Check if already starting to prevent duplicate operations
        if (CoinStarting)
        {
            Console.WriteLine("=== CoinStarting already true, ignoring call ===");
            return;
        }

        _isStartingFarm = true;

        try
        {
            // Check if user has PlutoPlus - if yes, skip trial logic entirely
            Console.WriteLine("About to check PlutoPlus permissions...");
            var hasPlutoPlus = await _permissionService.HasPermissionsAsync(LicensePermission.Plus);
            Console.WriteLine($"PlutoPlus check completed: {hasPlutoPlus}");

            if (hasPlutoPlus)
                // PlutoPlus users can farm without restrictions
                Console.WriteLine("PlutoPlus user - skipping trial");
            else
                try
                {
                    Console.WriteLine("Starting trial logic for non-PlutoPlus user...");

                    Console.WriteLine("About to call GetTrialInfoAsync...");

                    // Add a small delay to prevent rapid successive calls
                    await Task.Delay(100);

                    var trialInfo = await _trialService.GetTrialInfoAsync().ConfigureAwait(false);
                    Console.WriteLine($"GetTrialInfoAsync completed successfully - IsExpired: {trialInfo.IsExpired}");

                    if (trialInfo.IsExpired)
                    {
                        Console.WriteLine("Trial expired - showing dialog");
                        Console.WriteLine($"Farm component is null: {Farm == null}");
                        // Show trial dialog and stop execution
                        if (Farm != null) await Farm.ShowTrialDialogAsync().ConfigureAwait(false);
                        return;
                    }

                    Console.WriteLine($"Trial is active - {trialInfo.Uses}/{trialInfo.MaxUses} loves used");
                    Console.WriteLine("Trial validation completed - each successful pet love will count as 1 use");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Trial error: {ex.Message}");
                    // Show error in UI and stop
                    FarmEntities.Prepending(new FarmEntity
                    {
                        Time = DateTime.Now,
                        IsDefaultMessage = true,
                        DefaultMessage = $"Trial error: {ex.Message}"
                    });
                    Farm?.StateHasChangedAsync();
                    return;
                }

            Console.WriteLine("Trial validation completed - continuing to main farming logic");

            Console.WriteLine("Setting up new farming session...");
            Stopwatch = new Stopwatch();
            Stopwatch.Start();
            CoinTokenSource = new CancellationTokenSource();
            CoinRunning = true;
            CoinStarting = true;
            FarmEntities.Prepending(new FarmEntity
            {
                Time = DateTime.Now,
                IsDefaultMessage = true,
                DefaultMessage = "Starting.."
            });
            Console.WriteLine("Added 'Starting..' message, calling StateHasChanged");
            Farm?.StateHasChangedAsync();

            try
            {
                Console.WriteLine("Fetching pets data...");
                var pets = await _loginService.MspClient!.GetHighscoreBonsterAsync(a => a.UseCount(1000));
                if (!pets.Success)
                {
                    Console.WriteLine("Failed to fetch pets data");
                    StopCoin();
                    FarmEntities.Prepending(new FarmEntity
                    {
                        Time = DateTime.Now,
                        IsDefaultMessage = true,
                        DefaultMessage = "Farming stopped."
                    });
                    Farm?.StateHasChangedAsync();
                    await _dialogService.ShowToastAsync("Interaction to msp failed..");
                    return;
                }

                FarmEntities.Prepending(new FarmEntity
                {
                    Time = DateTime.Now,
                    IsDefaultMessage = true,
                    DefaultMessage = "Successfully fetched pets data"
                });
                Farm?.StateHasChangedAsync();

                CoinStarting = false;
                Console.WriteLine("CoinStarting set to false");
                Farm?.StateHasChangedAsync();

                // Start the cooldown timer for real-time UI updates
                StartCooldownTimer();

                Console.WriteLine("Starting pet processing...");

                // Enhanced pet filtering and processing
                var eligiblePetsList = GetOptimalPets(pets.Where(a => a.Level >= 40)).ToList();
                var processedCount = 0;
                var totalPets = eligiblePetsList.Count;

                FarmEntities.Prepending(new FarmEntity
                {
                    Time = DateTime.Now,
                    IsDefaultMessage = true,
                    DefaultMessage = $"Found {totalPets} eligible pets to process."
                });
                Farm?.StateHasChangedAsync();

                foreach (var t in eligiblePetsList.TakeWhile(_ => !CoinTokenSource.IsCancellationRequested))
                {
                    processedCount++;

                    // Update progress in activity log every 10 pets
                    if (processedCount % 10 == 0)
                    {
                        FarmEntities.Prepending(new FarmEntity
                        {
                            Time = DateTime.Now,
                            IsDefaultMessage = true,
                            DefaultMessage = $"Progress: {processedCount}/{totalPets} pets processed."
                        });
                        Farm?.StateHasChangedAsync();
                    }

                    retryPet:
                    try
                    {
                        var result = await t.LoveAsync();

                        if (result.IsRateLimited)
                        {
                            _lastRateLimit = DateTime.Now;
                            _consecutiveFailures++;

                            // Calculate enhanced rate limit duration
                            _currentRateLimitDuration =
                                _rateLimitCooldown.Add(TimeSpan.FromSeconds(_consecutiveFailures * 10));

                            FarmEntities.Prepending(new FarmEntity
                            {
                                Time = DateTime.Now,
                                IsRateLimited = true
                            });
                            Farm?.StateHasChangedAsync();

                            Console.WriteLine(
                                $"Rate limited - applying {_currentRateLimitDuration.TotalSeconds:F1}s delay (failure #{_consecutiveFailures})");

                            // Use the calculated rate limit duration
                            SetNextPetTime(_currentRateLimitDuration);
                            await Task.Delay(_currentRateLimitDuration, CoinTokenSource.Token);

                            if (!CoinTokenSource.IsCancellationRequested)
                                goto retryPet;

                            continue;
                        }

                        if (!result.Success)
                        {
                            _consecutiveFailures++;
                            CoinFailed++;

                            // Calculate exponential backoff delay
                            var backoffDelay = TimeSpan.FromSeconds(Math.Min(Math.Pow(2, _consecutiveFailures), 30));

                            FarmEntities.Prepending(new FarmEntity
                            {
                                Time = DateTime.Now,
                                IsFailed = true,
                                Status = result.Status
                            });

                            FarmEntities.Prepending(new FarmEntity
                            {
                                Time = DateTime.Now,
                                IsDefaultMessage = true,
                                DefaultMessage =
                                    $"Applying {backoffDelay.TotalSeconds:F1}s backoff delay (failure #{_consecutiveFailures})"
                            });
                            Farm?.StateHasChangedAsync();

                            Console.WriteLine(
                                $"Pet love failed - applying {backoffDelay.TotalSeconds:F1}s backoff delay (failure #{_consecutiveFailures})");

                            // Stop farming if too many consecutive failures
                            if (_consecutiveFailures >= _maxConsecutiveFailures)
                            {
                                StopCoin();
                                FarmEntities.Prepending(new FarmEntity
                                {
                                    Time = DateTime.Now,
                                    IsDefaultMessage = true,
                                    DefaultMessage =
                                        $"Stopped due to {_maxConsecutiveFailures} consecutive failures. Check your connection."
                                });
                                Farm?.StateHasChangedAsync();
                                return;
                            }

                            // Apply backoff delay before continuing
                            SetNextPetTime(backoffDelay);
                            await Task.Delay(backoffDelay, CoinTokenSource.Token);
                            continue;
                        }

                        // Reset consecutive failures on success
                        if (_consecutiveFailures > 0)
                        {
                            Console.WriteLine($"Success after {_consecutiveFailures} failures - resetting backoff");
                            _consecutiveFailures = 0;
                        }

                        // Cache the pet to prevent re-processing
                        if (CachedPet.All(a => a.Key != t.Id))
                            CachedPet.Add(t.Id, DateTime.Now);
                        else
                            CachedPet[t.Id] = DateTime.Now;

                        // Handle suspicious activity flag
                        if (result.Value == 1)
                        {
                            StopCoin();
                            FarmEntities.Prepending(new FarmEntity
                            {
                                Time = DateTime.Now,
                                IsDefaultMessage = true,
                                DefaultMessage = "MSP flagged your account as suspicious. Please try again later."
                            });
                            Farm?.StateHasChangedAsync();
                            return;
                        }

                        // Success - update stats
                        CoinEarned += result.Value;
                        _statsService.IncreaseStarCoins(result.Value);
                        FarmEntities.Prepending(new FarmEntity
                        {
                            Time = DateTime.Now,
                            Earned = result.Value
                        });
                        Farm?.StateHasChangedAsync();

                        // Add trial use for non-PlutoPlus users after successful pet love
                        if (!await _permissionService.HasPermissionsAsync(LicensePermission.Plus))
                            try
                            {
                                var updatedTrial = await _trialService.AddTrialUseAsync().ConfigureAwait(false);
                                Console.WriteLine(
                                    $"Trial use added: {updatedTrial.Uses}/{updatedTrial.MaxUses} loves used");

                                // Check if trial just expired
                                if (updatedTrial.IsExpired)
                                {
                                    Console.WriteLine("Trial limit reached - stopping farming");
                                    Console.WriteLine($"TrialDialog is null: {TrialDialog == null}");
                                    StopCoin();
                                    FarmEntities.Prepending(new FarmEntity
                                    {
                                        Time = DateTime.Now,
                                        IsDefaultMessage = true,
                                        DefaultMessage =
                                            $"Trial limit reached ({updatedTrial.MaxUses} loves). Farming stopped."
                                    });
                                    await Farm?.StateHasChangedAsync()!;

                                    // Use InvokeAsync to show dialog on UI thread
                                    if (Farm != null) await Farm.ShowTrialDialogAsync();
                                    return;
                                }
                            }
                            catch (Exception trialEx)
                            {
                                Console.WriteLine($"Failed to add trial use: {trialEx.Message}");
                                // Continue farming even if trial update fails
                            }

                        // Apply normal delay between successful pet loves
                        Console.WriteLine($"Success - applying normal {_baseDelay.TotalSeconds:F1}s delay");
                        SetNextPetTime(_baseDelay);
                        await Task.Delay(_baseDelay, CoinTokenSource.Token);
                    }
                    catch (OperationCanceledException)
                    {
                        // Expected when cancellation is requested
                        break;
                    }
                    catch (Exception ex)
                    {
                        _consecutiveFailures++;
                        CoinFailed++;

                        FarmEntities.Prepending(new FarmEntity
                        {
                            Time = DateTime.Now,
                            IsFailed = true,
                            Status = $"Unexpected error: {ex.Message}"
                        });
                        Farm?.StateHasChangedAsync();
                        // Add extra delay for unexpected errors
                        SetNextPetTime(TimeSpan.FromSeconds(5));
                        await Task.Delay(TimeSpan.FromSeconds(5), CoinTokenSource.Token);
                    }
                }

                StopCoin();
                FarmEntities.Prepending(new FarmEntity
                {
                    Time = DateTime.Now,
                    IsDefaultMessage = true,
                    DefaultMessage = "Farming Completed."
                });
                Farm?.StateHasChangedAsync();
            }
            catch (Exception ex)
            {
                StopCoin();
                FarmEntities.Prepending(new FarmEntity
                {
                    Time = DateTime.Now,
                    IsDefaultMessage = true,
                    DefaultMessage = $"Farming error: {ex.Message}"
                });
                Farm?.StateHasChangedAsync();
                await _dialogService.ShowToastAsync($"Farming error: {ex.Message}");
            }
        }
        catch (Exception globalEx)
        {
            Console.WriteLine($"=== GLOBAL ERROR in StartCoinAsync: {globalEx.Message} ===");
            Console.WriteLine($"Stack trace: {globalEx.StackTrace}");

            FarmEntities.Prepending(new FarmEntity
            {
                Time = DateTime.Now,
                IsDefaultMessage = true,
                DefaultMessage = $"Global error: {globalEx.Message}"
            });
            Farm?.StateHasChangedAsync();
        }
        finally
        {
            _isStartingFarm = false;
            Console.WriteLine("=== StartCoinAsync completed, flag reset ===");
        }
    }

    public void Clear()
    {
        FarmEntities.Clear();
        Farm?.StateHasChangedAsync();
    }

    /// <summary>
    ///     Test method to manually show the trial dialog (for testing purposes)
    /// </summary>
    public void TestShowTrialDialog()
    {
        Console.WriteLine($"TestShowTrialDialog called - TrialDialog is null: {TrialDialog == null}");
        if (TrialDialog != null)
        {
            Console.WriteLine("Calling TrialDialog.Show()...");
            TrialDialog.Show();
            Console.WriteLine("TrialDialog.Show() called successfully");
        }
        else
        {
            Console.WriteLine("TrialDialog is null - cannot show dialog");
        }
    }

    /// <summary>
    ///     Test method to manually expire the trial (for testing purposes)
    /// </summary>
    public async Task TestExpireTrialAsync()
    {
        await _trialService.ExpireTrialAsync().ConfigureAwait(false);
        FarmEntities.Prepending(new FarmEntity
        {
            Time = DateTime.Now,
            IsDefaultMessage = true,
            DefaultMessage = "Trial manually expired for testing."
        });
        Farm?.StateHasChangedAsync();
    }

    /// <summary>
    ///     Test method to reset corrupted trial (for testing purposes)
    /// </summary>
    public async Task TestResetTrialAsync()
    {
        await _trialService.ResetTrialAsync().ConfigureAwait(false);
        FarmEntities.Prepending(new FarmEntity
        {
            Time = DateTime.Now,
            IsDefaultMessage = true,
            DefaultMessage = "Trial reset for testing."
        });
        Farm?.StateHasChangedAsync();
    }

    public static void Dispose()
    {
        if (_farmService is null)
            return;

        _farmService.Stopwatch?.Reset();
        _farmService.Stopwatch?.Stop();
        _farmService._cooldownTimer?.Dispose();
        _farmService._cooldownTimer = null;
        _farmService.StopCoin();
        _farmService.CoinEarned = 0;
        _farmService.CoinFailed = 0;
        _farmService._consecutiveFailures = 0;
        _farmService._lastRateLimit = DateTime.MinValue;
        _farmService._nextPetTime = DateTime.MinValue;
        _farmService.FarmEntities.Clear();
        _farmService.CachedPet.Clear();
    }
}