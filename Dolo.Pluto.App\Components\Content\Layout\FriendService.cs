﻿using Dolo.Nebula;
using Dolo.Planet.Entities;
using Dolo.Planet.Enums;
using Dolo.Pluto.App.Components.Content.Component;
using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.App.Core.Extension;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class FriendService : IService
{
    private static FriendService? _instance;
    private readonly DialogService _dialogService;
    private readonly LoginService _loginService;
    private CancellationTokenSource _cancellationTokenSource;

    public FriendService(DialogService dialogService, LoginService loginService)
    {
        _cancellationTokenSource = new CancellationTokenSource();
        _dialogService = dialogService;
        _loginService = loginService;
        _instance = this;
    }


    public Friend? Friend { get; set; }
    public bool IsLoading { get; private set; }
    public bool IsDeleting { get; private set; }
    public List<MspActor> Friends { get; private set; } = [];
    public List<int> SelectedOptions { get; set; } = [0];
    public string? Days { get; set; }
    public string? Level { get; set; }

    public bool HasOption(int id)
    {
        return SelectedOptions.Any(a => a == id);
    }

    public void AssignOptions(int id)
    {
        if (id == 0)
        {
            if (HasOption(0))
            {
                SelectedOptions.Remove(0);
                return;
            }

            SelectedOptions.Clear();
            SelectedOptions.Add(id);
            return;
        }

        if (id == 1 && HasOption(2))
            SelectedOptions.Remove(2);

        if (id == 2 && HasOption(1))
            SelectedOptions.Remove(1);

        if (id == 3 && HasOption(4))
            SelectedOptions.Remove(4);

        if (id == 4 && HasOption(3))
            SelectedOptions.Remove(3);

        if (id == 6 && HasOption(9))
            SelectedOptions.Remove(9);

        if (id == 9 && HasOption(6))
            SelectedOptions.Remove(6);

        if (id == 7 && HasOption(10))
            SelectedOptions.Remove(10);

        if (id == 10 && HasOption(7))
            SelectedOptions.Remove(7);

        if (id == 8 && HasOption(11))
            SelectedOptions.Remove(11);

        if (id == 11 && HasOption(8))
            SelectedOptions.Remove(8);

        if (SelectedOptions.Contains(0))
            SelectedOptions.Remove(0);

        if (SelectedOptions.Contains(id))
            SelectedOptions.Remove(id);
        else
            SelectedOptions.Add(id);

        if (!SelectedOptions.Any())
            SelectedOptions.Add(0);
    }

    /// <summary>
    ///     Update the friend status inside the list
    /// </summary>
    /// <param name="actor"></param>
    /// <param name="type"></param>
    private void UpdateFriendStatus(MspActor actor, FriendStatusType type)
    {
        actor.FriendStatus = type;

        var friend = Friends.SingleOrDefault(a => a.Id == actor.Id);
        if (friend is null) return;

        friend.FriendStatus = type;
    }

    /// <summary>
    ///     Async method to load all friends from the server
    /// </summary>
    public async Task LoadFriendsAsync(bool isForceReload = false)
    {
        if (_loginService.MspClient is null)
            return;

        if (Friends.Any() && !isForceReload)
            return;

        if (IsLoading && !isForceReload)
            return;

        SetLoading(true);
        Friends = await _loginService.MspClient.GetFriendsWithActorAsync(false);
        SetLoading(false);
    }


    /// <summary>
    ///     Deletes a single friend
    /// </summary>
    private async Task DeleteSingleAsync(MspActor friend)
    {
        var nebula = new NebulaClient(a =>
        {
            a.EnableAuthInformation()
                .SetAuthToken(_loginService.MspUser.Actor.Nebula?.AccessToken)
                .SetRefreshToken(_loginService.MspUser.Actor.Nebula?.RefreshToken)
                .SetClient(_loginService.MspClient)
                .SetNebula(_loginService.MspUser.Actor.Nebula);
        });

        IsDeleting = true;
        Friend?.StateHasChangedAsync();
        if (!await nebula.TryInitializeAsync())
        {
            IsDeleting = false;
            Friend?.StateHasChangedAsync();
            await _dialogService.ShowToastAsync("Failed to interact with msp ..", ToastType.Error);
            return;
        }

        var friends = await nebula.DeleteFriendAsync(friend.ProfileId!);
        if (!friends.Result)
        {
            IsDeleting = false;
            Friend?.StateHasChangedAsync();
            _dialogService.ShowExceptionLog(new ExceptionInfo()
                .AddTrace($"Failed to delete {friend.Username} ..")
                .SetCategory("Friend System")
                .SetStatusCode((int?)friends.Response?.StatusCode ?? 0));
            await _dialogService.ShowToastAsync($"Failed to delete {friend.Username} ..", ToastType.Error);
            return;
        }

        IsDeleting = false;
        var friendIndex = _loginService.MspUser.Actor.Friends.SingleOrDefault(a => a.Id == friend.Id);
        if (friendIndex != null)
            _loginService.MspUser.Actor.Friends.Remove(friendIndex);

        //UpdateFriendStatus(friend, FriendStatusType.NOT_FRIENDS);
        RemoveFriend(friend);

        await MSPUserPopup.Instance!.StateHasChangedAsync();
        await _dialogService.ShowToastAsync($"{friend.Username} has been deleted.", ToastType.Success);
    }

    /// <summary>
    ///     Delete many friends
    /// </summary>
    private async Task DeleteManyAsync()
    {
        var nebula = new NebulaClient(a =>
        {
            a.EnableAuthInformation()
                .SetAuthToken(_loginService.MspUser.Actor.Nebula?.AccessToken)
                .SetRefreshToken(_loginService.MspUser.Actor.Nebula?.RefreshToken)
                .SetClient(_loginService.MspClient)
                .SetNebula(_loginService.MspUser.Actor.Nebula);
        });

        IsDeleting = true;
        Friend?.StateHasChangedAsync();
        if (!await nebula.TryInitializeAsync())
        {
            IsDeleting = false;
            Friend?.StateHasChangedAsync();
            await _dialogService.ShowToastAsync("Failed to interact with msp ..", ToastType.Error);
            return;
        }

        var filteredFriends = GetFilteredFriends();
        if (!filteredFriends.Any())
        {
            IsDeleting = false;
            Friend?.StateHasChangedAsync();
            await _dialogService.ShowToastAsync("No friends to delete ..", ToastType.Error);
            return;
        }

        var deleted = 0;
        var code = 0;
        var exceptions = new List<string>();
        foreach (var friend in filteredFriends.TakeWhile(_ => !_cancellationTokenSource.IsCancellationRequested))
        {
            var delete = await nebula.DeleteFriendAsync(friend.ProfileId!);
            if (!delete.Result)
            {
                code = (int?)delete.Response?.StatusCode ?? 0;
                exceptions.Add(
                    $"Failed to delete '{friend.Username}' got status code '{delete.Response?.StatusCode}' ..");
                continue;
            }

            deleted++;
            RemoveFriend(friend);
        }

        if (deleted == 0)
        {
            IsDeleting = false;
            Friend?.StateHasChangedAsync();
            _dialogService.ShowExceptionLog(new ExceptionInfo()
                .SetCategory("Friend System")
                .SetStatusCode(code)
                .AddTrace(exceptions));
            return;
        }

        IsDeleting = false;
        Friend?.StateHasChangedAsync();
        await _dialogService.ShowToastAsync($"{deleted} friends has been deleted.", ToastType.Success);
    }

    /// <summary>
    ///     Shows the modal which ask for single delete confirmation
    /// </summary>
    public void AskForSingleDeletion(MspActor friend)
    {
        _dialogService.ShowPopup($"Are you sure you want to delete '{friend.Username}'?",
            async () => await DeleteSingleAsync(friend));
    }


    /// <summary>
    ///     Shows the modal which ask for many delete confirmation
    /// </summary>
    public void AskForManyDeletion()
    {
        _dialogService.ShowPopup(
            $"Are you sure you want to delete '{GetFilteredFriends().Count:N0}' friends with option '{GetFilteredFriendOptionName()}'?",
            async () => await DeleteManyAsync());
    }


    /// <summary>
    ///     Private method to set the loading state
    /// </summary>
    /// <param name="loading"></param>
    private void SetLoading(bool loading)
    {
        IsLoading = loading;
        Friend?.StateHasChangedAsync();
    }


    private void RemoveFriend(MspActor friend)
    {
        var index = Friends.FindIndex(a => a.Id == friend.Id);
        if (index == -1)
            return;

        Friends.RemoveAt(index);
        Friend?.StateHasChangedAsync();
    }

    public List<List<MspActor>> GetSortedFriends()
    {
        return GetFilteredFriends().SortInRows(3);
    }

    /// <summary>
    ///     Get the filtered friends
    /// </summary>
    /// <returns></returns>
    public List<MspActor> GetFilteredFriends()
    {
        if (SelectedOptions.Contains(0))
            return Friends.ToList();

        var friends = Friends.ToList();
        foreach (var option in SelectedOptions)
            friends = option switch
            {
                0 => Friends.ToList(),
                1 => Friends.Where(x => x.IsVip).ToList(),
                2 => Friends.Where(x => !x.IsVip).ToList(),
                3 => Friends.Where(x => x.Gender == Gender.Female).ToList(),
                4 => Friends.Where(x => x.Gender == Gender.Male).ToList(),
                5 => Friends.Where(x => x.IsDeleted).ToList(),
                6 => Friends.Where(x => x.IsCeleb).ToList(),
                7 => Friends.Where(x => x.IsJury).ToList(),
                8 => Friends.Where(x => x.IsJudge).ToList(),
                9 => Friends.Where(x => !x.IsCeleb).ToList(),
                10 => Friends.Where(x => !x.IsJury).ToList(),
                11 => Friends.Where(x => !x.IsJudge).ToList(),
                12 => Friends.Where(x => x.Level == (int.TryParse(Level, out var val) ? val : 0)).ToList(),
                13 => Friends.Where(x => x.InActiveSince.Days >= (int.TryParse(Days, out var val) ? val : 0)).ToList(),
                _ => Friends.ToList()
            };

        return friends;
    }

    /// <summary>
    ///     Get the filtered friends option names
    /// </summary>
    /// <returns></returns>
    private string GetFilteredFriendOptionName()
    {
        return string.Join(",", SelectedOptions.Select(option => option switch
        {
            0 => "All",
            1 => "VIP",
            2 => "Non-VIP",
            3 => "Female",
            4 => "Male",
            5 => "Deleted",
            6 => "Celeb",
            7 => "Jury",
            8 => "Judge",
            9 => "Non-Celeb",
            10 => "Non-Jury",
            11 => "Non-Judge",
            12 => $"Level {Level}",
            13 => $"Inactive {Days} days",
            _ => "none"
        }));
    }

    public static void Dispose()
    {
        if (_instance == null)
            return;

        _instance._cancellationTokenSource.Cancel();
        _instance._cancellationTokenSource = new CancellationTokenSource();
        _instance.Friends.Clear();
        _instance.SelectedOptions.Clear();
    }
}