﻿using Dolo.Core.Consola;
using Dolo.Core.Http;
using Dolo.Pluto.Shard.Services;
using Titanium.Web.Proxy.EventArguments;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class InterceptService : IService
{
    public readonly HttpIntercept _intercept = new();
    public readonly List<InterceptSession> _sessions = new();
    public InterceptSession? _session;
    public Intercept? Intercept;
    public int PageId = 0;

    /// <summary>
    ///     Initialize the intercept events
    /// </summary>
    public void Initialize()
    {
        _intercept.OnRequest += OnRequest;
        _intercept.OnResponse += OnResponse;
        _intercept.OnAfterResponse += OnAfterResponse;
    }

    /// <summary>
    ///     Event fired when a request is received from the client
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async Task OnAfterResponse(object sender, SessionEventArgs e)
    {
        if (!e.HttpClient.Request.Url.Contains("mspapis.com/msp/")) return;
        Consola.Debug($"AfterResponse: {e.HttpClient.Request.Url}");
    }

    /// <summary>
    ///     Event fired when a response is received from the server
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async Task OnResponse(object sender, SessionEventArgs e)
    {
        if (!e.HttpClient.Request.Url.Contains("mspapis.com/msp/")) return;
        Consola.Debug($"OnResponse: {e.HttpClient.Request.Url}");
    }

    /// <summary>
    ///     Event fired when a request is sent to the client
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private async Task OnRequest(object sender, SessionEventArgs e)
    {
        if (!e.HttpClient.Request.Url.Contains("mspapis.com/msp/")) return;

        _sessions.Add(new InterceptSession(e));
        Intercept?.StateHasChangedAsync();

        Consola.Debug($"OnRequest: {e.HttpClient.Request.Url}");
    }

    /// <summary>
    ///     Intercept the traffic with start stop functionality
    /// </summary>
    public async Task Intercepting()
    {
        await _intercept.EnsureCertificateAsync();

        if (_intercept.IsIntercepting)
            _intercept.Stop();
        else
            _intercept.Start();
    }

    /// <summary>
    ///     clears the sessions
    /// </summary>
    public void Clear()
    {
        _sessions.Clear();
        _session = null;
    }

    /// <summary>
    ///     Dispose the intercept
    /// </summary>
    public void Dispose()
    {
        _intercept.Dispose();
    }

    /// <summary>
    ///     Unregister the intercept events
    /// </summary>
    public void Unregister()
    {
        _intercept.OnRequest -= OnRequest;
        _intercept.OnResponse -= OnResponse;
        _intercept.OnAfterResponse -= OnAfterResponse;
    }
}