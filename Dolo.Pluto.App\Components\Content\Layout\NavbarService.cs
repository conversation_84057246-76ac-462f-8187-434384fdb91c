﻿using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class NavbarService : IService
{
    /// <summary>
    ///     The main component we need this to render the component
    /// </summary>
    public Main? Main { get; set; }

    /// <summary>
    ///     The page id of the current page
    /// </summary>
    public NavbarPage Page { get; set; }

    /// <summary>
    ///     Method to set the page id
    /// </summary>
    public void SetPageId(NavbarPage page)
    {
        Page = page;
        Main?.StateHasChangedAsync();
    }
}