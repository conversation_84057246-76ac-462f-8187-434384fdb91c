﻿using Dolo.Planet.Enums;
using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class PlutoPlusRecyclerService(LoginService loginService, DialogService dialogService) : IService
{
    public PlutoPlus_Recycler? PlutoPlusRecycler;
    public int Amount { get; set; } = 1;
    public int Generated { get; set; }
    public bool IsGenerating { get; set; }

    private int GetItemId()
    {
        return loginService.MspUser.Actor.Gender == Gender.Female ? 5557 : 3846;
    }

    public async Task GenerateAsync()
    {
        IsGenerating = true;
        PlutoPlusRecycler?.StateHasChangedAsync();
        Generated = 0;

        var amount = Amount;
        var tasks = new List<Task>();
        for (var i = 0; i < Amount; i++)
            tasks.Add(_ = Task.Run(async () =>
            {
                var item = await loginService.MspClient!.SubmitMobileStartupRewardAsync(GetItemId(), true);
                if (!item.IsSuccessStatusCode)
                    amount--;
            }));

        await Task.WhenAll(tasks);
        tasks.Clear();

        var semaphore = new SemaphoreSlim(1);
        var actorItems = await loginService.MspClient!.GetActorItemsAsync();
        var recyclingItems = actorItems.Where(a => a.ClothId == GetItemId()).ToList();

        for (var i = 0; i < amount; i++)
            tasks.Add(_ = Task.Run(async () =>
            {
                if (recyclingItems.Count == 0)
                    return;

                await semaphore.WaitAsync();

                var itemToRecycle = recyclingItems.FirstOrDefault();
                if (itemToRecycle == null)
                {
                    semaphore.Release();
                    return;
                }

                recyclingItems.Remove(itemToRecycle);

                var recycle = await loginService.MspClient.RecycleItemAsync(itemToRecycle.Id);
                if (recycle is { Success: true, Value: > 0 })
                    Generated = recycle.Value;

                semaphore.Release();
                PlutoPlusRecycler?.StateHasChangedAsync();
            }));

        await Task.WhenAll(tasks);


        IsGenerating = false;
        PlutoPlusRecycler?.StateHasChangedAsync();

        await dialogService.ShowToastAsync($"You now have {Generated:N0} Recycle Points");
    }
}