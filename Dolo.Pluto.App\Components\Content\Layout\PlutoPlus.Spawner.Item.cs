﻿using Dolo.Planet.Enums;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class PlutoPlusSpawnerItem(string? name, int id, Gender gender, bool isTop, string? imageUrl)
{
    public string? ImageUrl { get; set; } = imageUrl;
    public string? Name { get; set; } = name;
    public int ItemId { get; set; } = id;
    public bool IsTop { get; set; } = isTop;
    public Gender Gender { get; set; } = gender;
}