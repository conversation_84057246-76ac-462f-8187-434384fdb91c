﻿using Dolo.Planet.Enums;
using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class PlutoPlusSpawnerService(LoginService loginService, DialogService dialogService) : IService
{
    public PlutoPlus_Spawner? PlutoPlusSpawner;
    public PlutoPlusSpawnerItem? SelectedItem;
    public bool IsGenerating { get; set; }
    public int Generated { get; set; }
    public int Amount { get; set; } = 1;

    public List<PlutoPlusSpawnerItem> Items { get; set; } =
    [
        new("Lotsa Kisses", 5557, Gender.Female, true, Assets.MSPShirtFemaleA),
        new("Monster Madness", 3921, Gender.Female, true, Assets.MSPShirtFemaleB),
        new("Backstreet Girl", 4501, Gender.Female, true, Assets.MSPShirtFemaleC),
        new("Cute Basic Skirt", 2852, Gender.Female, false, Assets.MSPPantsFemaleA),
        new("Party People", 4691, Gender.Female, false, Assets.MSPPantsFemaleB),
        new("Summer Shorts", 3985, Gender.Female, false, Assets.MSPPantsFemaleC),
        new("City Life", 3846, Gender.Male, true, Assets.MSPShirtMaleA),
        new("Cool Customer", 5581, Gender.Male, true, Assets.MSPShirtMaleB),
        new("Burger Shirt", 2860, Gender.Male, true, Assets.MSPShirtMaleC),
        new("Baggy Jeans", 1708, Gender.Male, false, Assets.MSPPantsMaleA),
        new("Tie Dye Jeans", 3207, Gender.Male, false, Assets.MSPPantsMaleB),
        new("Tropical Sunrise", 2110, Gender.Male, false, Assets.MSPPantsMaleC)
    ];

    public async Task SpawnAsync()
    {
        if (SelectedItem is null)
        {
            await dialogService.ShowToastAsync("Please select an Item", ToastType.Error);
            return;
        }

        IsGenerating = true;
        PlutoPlusSpawner?.StateHasChangedAsync();
        Generated = 0;

        for (var i = 0; i < Amount; i++)
        {
            var req = await loginService.MspClient!.SubmitMobileStartupRewardAsync(SelectedItem.ItemId,
                SelectedItem.IsTop);
            if (req is { Success: true, IsSuccessStatusCode: true })
                Generated++;

            PlutoPlusSpawner?.StateHasChangedAsync();
        }

        IsGenerating = false;
        PlutoPlusSpawner?.StateHasChangedAsync();

        await dialogService.ShowToastAsync($"Spawned {Generated}/{Amount} items");
    }
}