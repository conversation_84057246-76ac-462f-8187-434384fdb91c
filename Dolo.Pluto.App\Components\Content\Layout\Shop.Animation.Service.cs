﻿using Dolo.Planet.Entities;
using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.App.Core.Extension;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class ShopAnimationService : IService
{
    private static ShopAnimationService? _animation;
    private readonly DialogService _dialogService;
    private readonly LoginService _loginService;

    public ShopAnimationService(LoginService loginService, DialogService dialogService)
    {
        _loginService = loginService;
        _dialogService = dialogService;
        _animation = this;
    }

    public int Category { get; set; }
    private MspList<IEnumerable<MspAnimation>> AllAnimations { get; } = new();
    private MspList<IEnumerable<MspAnimation>> AnimalAnimations { get; } = new();
    private MspList<IEnumerable<MspAnimation>> PartyAnimations { get; } = new();
    private MspList<IEnumerable<MspAnimation>> SportAnimations { get; } = new();
    private MspList<IEnumerable<MspAnimation>> WildAnimations { get; } = new();
    private MspList<IEnumerable<MspAnimation>> LoveAnimations { get; } = new();
    private MspList<IEnumerable<MspAnimation>> BasicAnimations { get; } = new();
    private MspList<IEnumerable<MspAnimation>> DanceAnimations { get; } = new();
    private MspList<IEnumerable<MspAnimation>> PoseAnimations { get; } = new();
    private MspList<IEnumerable<MspAnimation>> FunAnimations { get; } = new();
    private MspList<IEnumerable<MspAnimation>> FightAnimations { get; } = new();
    private MspList<IEnumerable<MspAnimation>> StarCoinAnimations { get; } = new();
    private MspList<IEnumerable<MspAnimation>> DiamondAnimations { get; } = new();
    private MspList<IEnumerable<MspAnimation>> VipAnimations { get; } = new();
    private Shop_Animation? AnimationShop { get; }
    public MspAnimation? SelectedAnimation { get; set; }
    public int SelectedAnimationId { get; set; }
    public bool IsLoading { get; set; }
    public bool IsCustom { get; set; }
    public string? AnimationFilterName { get; set; }

    private MspList<IEnumerable<MspAnimation>> GetAnimationsFromCategory()
    {
        return Category switch
        {
            0 => AllAnimations,
            1 => AnimalAnimations,
            2 => PartyAnimations,
            3 => SportAnimations,
            4 => WildAnimations,
            5 => LoveAnimations,
            6 => BasicAnimations,
            7 => DanceAnimations,
            8 => PoseAnimations,
            9 => FunAnimations,
            10 => FightAnimations,
            11 => StarCoinAnimations,
            12 => DiamondAnimations,
            13 => VipAnimations,
            _ => AllAnimations
        };
    }

    public string GetAnimationCategoryName()
    {
        return Category switch
        {
            0 => "Every Animation",
            1 => "Animal Animation",
            2 => "Party Animation",
            3 => "Sport Animation",
            4 => "Wild Animation",
            5 => "Love Animation",
            6 => "Basic Animation",
            7 => "Dance Animation",
            8 => "Pose Animation",
            9 => "Fun Animations",
            10 => "Fight Animation",
            11 => "Every StarCoin Animation",
            12 => "Every Diamond Animation",
            13 => "Every VIP Animation",
            _ => "Every Animation"
        };
    }

    /// <summary>
    ///     Get the animations filtered by the name
    /// </summary>
    public List<IEnumerable<MspAnimation>> GetAnimationFiltered()
    {
        if (string.IsNullOrEmpty(AnimationFilterName))
            return GetAnimationsFromCategory();

        var animations = GetAnimationsFromCategory().ToList();
        animations.RemoveAll(a =>
            !a.Any(a2 => a2.Name != null && a2.Name.ToLower().Contains(AnimationFilterName.ToLower())));

        return animations;
    }

    private List<List<MspAnimation>> SetAnimationSortedCategory(int id, IEnumerable<MspAnimation> animations)
    {
        return animations.Where(a => a.CategoryId == id).ToList().SortInRows(4);
    }

    private List<List<MspAnimation>> SetAnimationSortedPrice(bool isStarCoin, MspList<MspAnimation> animations)
    {
        return animations.Where(a => isStarCoin ? a.Price != 0 : a.DiamondsPrice != 0).ToList().SortInRows(4);
    }

    private List<List<MspAnimation>> SetAnimationSortedCategoryVip(MspList<MspAnimation> animations)
    {
        return animations.Where(a => a.IsVip).ToList().SortInRows(4);
    }


    /// <summary>
    ///     Loads the animations from the server
    /// </summary>
    public async Task LoadAnimationsAsync()
    {
        if (GetAnimationsFromCategory().Any())
            return;

        IsLoading = true;
        AnimationShop?.StateHasChangedAsync();

        var animations = await _loginService.MspClient!.GetAnimationsAsync();

        AllAnimations.AddRange(animations.SortInRows(4));
        AnimalAnimations.AddRange(SetAnimationSortedCategory(22, animations));
        PartyAnimations.AddRange(SetAnimationSortedCategory(19, animations));
        SportAnimations.AddRange(SetAnimationSortedCategory(21, animations));
        WildAnimations.AddRange(SetAnimationSortedCategory(20, animations));
        LoveAnimations.AddRange(SetAnimationSortedCategory(15, animations));
        BasicAnimations.AddRange(SetAnimationSortedCategory(23, animations));
        DanceAnimations.AddRange(SetAnimationSortedCategory(14, animations));
        PoseAnimations.AddRange(SetAnimationSortedCategory(17, animations));
        FunAnimations.AddRange(SetAnimationSortedCategory(16, animations));
        FightAnimations.AddRange(SetAnimationSortedCategory(16, animations));
        StarCoinAnimations.AddRange(SetAnimationSortedPrice(true, animations));
        DiamondAnimations.AddRange(SetAnimationSortedPrice(false, animations));
        VipAnimations.AddRange(SetAnimationSortedCategoryVip(animations));
        IsLoading = false;
        AnimationShop?.StateHasChangedAsync();
    }

    /// <summary>
    ///     Shows the modal which ask for single delete confirmation
    /// </summary>
    public void AskForBuyingAnimation()
    {
        _dialogService.ShowPopup(
            $"Do you really want to buy '{(IsCustom ? $"custom ({SelectedAnimationId})" : SelectedAnimation?.Name)}' animation?",
            async () => await BuyAnimationAsync());
    }

    /// <summary>
    ///     Buys a animation
    /// </summary>
    private async Task BuyAnimationAsync()
    {
        if (SelectedAnimation?.Id == 0)
        {
            await _dialogService.ShowToastAsync("Please select a animation", ToastType.Error);
            return;
        }

        await _dialogService.ShowLoaderAsync();
        var bought =
            await _loginService.MspClient!.BuyAnimationAsync(IsCustom ? SelectedAnimationId : SelectedAnimation!.Id);
        if (!bought.Success)
        {
            await _dialogService.HideLoaderAsync();
            await _dialogService.ShowToastAsync($"Failed to buy animation {bought.Status}..", ToastType.Error);
            return;
        }

        if (!bought.HasBought)
        {
            await _dialogService.HideLoaderAsync();
            await _dialogService.ShowToastAsync(bought.GetDescriptionName(), ToastType.Error);
            return;
        }

        await _dialogService.HideLoaderAsync();
        await _dialogService.ShowToastAsync("Animation bought", ToastType.Success);
    }

    public static void Dispose()
    {
        if (_animation is null) return;

        _animation.IsCustom = false;
        _animation.SelectedAnimation = null;
    }
}