﻿using Dolo.Core.Extension;
using Dolo.Planet.Entities.Beauty;
using Dolo.Planet.Enums;
using Dolo.Pluto.App.Components.Content.Component;
using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class ShopBeautyService : IService
{
    private static ShopBeautyService? _instance;
    private readonly DialogService _dialogService;
    private readonly LoginService _loginService;

    public ShopBeautyService(DialogService dialogService, LoginService loginService)
    {
        _dialogService = dialogService;
        _loginService = loginService;
        _instance = this;
    }

    public BeautyHeadBoy? BeautyHeadBoy { get; set; }
    public BeautyHeadGirl? BeautyHeadGirl { get; set; }
    public bool IsBeautyCustomColor { get; set; }
    public string? SelectedBeautyColor { get; set; } = "#984F2B";
    public string? SelectedBeautyCustomColor { get; set; }

    public void AskForBuyingBeauty()
    {
        _dialogService.ShowPopup(
            $"Do you really want to buy '{(IsBeautyCustomColor ? $"custom skin color '{SelectedBeautyCustomColor}'" : $"skin color '{SelectedBeautyColor}'")} ?",
            async () => await BuyBeautyAsync());
    }

    /// <summary>
    ///     Buys a beauty skin color
    /// </summary>
    private async Task BuyBeautyAsync()
    {
        if (!IsBeautyCustomColor && string.IsNullOrEmpty(SelectedBeautyColor))
        {
            await _dialogService.ShowToastAsync("Please select a color", ToastType.Error);
            return;
        }

        if (IsBeautyCustomColor && string.IsNullOrEmpty(SelectedBeautyCustomColor))
        {
            await _dialogService.ShowToastAsync("Please select a custom color", ToastType.Error);
            return;
        }


        await _dialogService.ShowLoaderAsync();

        var result = await _loginService.MspClient!.BuyBeautyClinicAsync(new MspBeautyItem(BeautyType.Skins)
            .SetColors(IsBeautyCustomColor
                ? SelectedBeautyCustomColor![1..].ToInt().ToString()
                : SelectedBeautyColor![1..].ToInt().ToString()));

        if (!result.Success)
        {
            await _dialogService.HideLoaderAsync();
            await _dialogService.ShowToastAsync($"failed to send request to ms {result.Status}", ToastType.Error);
            return;
        }

        if (!result.Value?.HasSuccessfullyBought() ?? false)
        {
            await _dialogService.HideLoaderAsync();
            await _dialogService.ShowToastAsync($"failed to buy the skin color {result.Status}", ToastType.Error);
            return;
        }

        await _dialogService.HideLoaderAsync();
        await _dialogService.ShowToastAsync("New skin color has been updated. restart msp", ToastType.Success);
    }

    public static void Dispose()
    {
        if (_instance is null) return;

        _instance.SelectedBeautyCustomColor = "#ffffff";
        _instance.SelectedBeautyColor = "#984F2B";
        _instance.IsBeautyCustomColor = false;
    }
}