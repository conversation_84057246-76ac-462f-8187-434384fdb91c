﻿@using Dolo.Planet.Enums
@using Dolo.Pluto.App.Components.Content.Component
@using Dolo.Pluto.App.Components.Content.Pages
@inject DialogService DialogService
@inject ShopBeautyService BeautyService
@inject LoginService LoginService

<div
    class="@(_isShown ? "opacity-100 animate-pop-in" : "opacity-0 pointer-events-none") z-10 absolute flex h-full w-full flex-col items-center bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100 bg-[url(@(Dolo.Assets.MSPBackgroundMSPBSP))] bg-contain">
    <!-- Balanced animated background gradients -->
    <div class="absolute inset-0 overflow-hidden">
        <div
            class="absolute -top-40 -left-40 w-80 h-80 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-40 animate-pulse"></div>
        <div
            class="absolute -bottom-40 -right-40 w-80 h-80 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-40 animate-pulse"
            style="animation-delay: 2s"></div>
        <div
            class="absolute top-1/2 left-1/2 w-80 h-80 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-35 animate-pulse"
            style="animation-delay: 4s"></div>
    </div>

    <div class="relative flex h-full w-full flex-col items-center bg-white/70 backdrop-blur-sm">
        <div class="grid h-full w-full p-2 [grid-template-rows:auto_1fr]">
            <!-- Balanced header with better contrast -->
            <div
                class="flex items-center justify-between p-2 bg-white/95 backdrop-blur-md rounded-lg border border-slate-300 shadow-lg mb-2">
                <div class="flex items-center space-x-2">
                    <div
                        class="w-6 h-6 bg-gradient-to-br from-pink-500 to-purple-600 rounded-md flex items-center justify-center shadow-md">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-white" fill="none"
                             viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"/>
                        </svg>
                    </div>
                    <div>
                        <h1 class="font-semibold text-md text-slate-800 font-bold">Beauty Clinic</h1>
                        <p class="text-slate-600 text-sm font-['blue_highway']">Transform your look</p>
                    </div>
                </div>
                <div @onclick="Hide"
                     class="group cursor-pointer p-1.5 bg-slate-100 hover:bg-slate-200 rounded-md transition-all duration-200 border border-slate-200 hover:border-slate-300">
                    <svg xmlns="http://www.w3.org/2000/svg"
                         class="h-3 w-3 text-slate-600 group-hover:rotate-90 transition-transform duration-200"
                         fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </div>
            </div>
            <!-- Main content grid -->
            <div class="grid h-full w-full gap-3 [grid-template-columns:1fr_260px]">
                <!-- Color palette section with fixed scrolling -->
                <div class="relative flex flex-col h-full">
                    <div
                        class="bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200 p-4 flex flex-col h-[460px]">
                        <div class="flex items-center justify-between mb-3">
                            <h2 class="text-gray-800 text-lg font-semibold">Color Palette</h2>
                            <div class="text-gray-500 text-sm">
                                @(_items.Count) colors
                            </div>
                        </div>

                        <!-- Color grid wrapper: flex-1 min-h-0 overflow-y-auto for strict height constraint and scrolling -->
                        <div
                            class="bg-gray-50 rounded-lg border border-gray-200 p-2 flex-1 min-h-0 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent @(BeautyService.IsBeautyCustomColor ? "pointer-events-none opacity-50" : "")">
                            <!-- Grid: auto-size to content -->
                            <div class="grid grid-cols-6 gap-3">
                                @foreach (var item in _items)
                                {
                                    <div @onclick="() => SetSelectedColor(item.Color)"
                                         class="group relative flex flex-col items-center justify-center rounded-lg bg-white border border-gray-200 hover:border-gray-300 p-1.5 shadow-sm transition-all duration-200 hover:shadow-md cursor-pointer @(BeautyService.SelectedBeautyColor == item.Color && !BeautyService.IsBeautyCustomColor ? "ring-2 ring-pink-500 ring-offset-1 ring-offset-white" : "")">

                                        <!-- Color preview -->
                                        <div class="relative mb-1">
                                            <div
                                                class="w-8 h-8 rounded-md bg-[@item.Color] shadow-sm border border-gray-200 group-hover:border-gray-300 transition-all duration-200"></div>
                                            @if (BeautyService.SelectedBeautyColor == item.Color && !BeautyService.IsBeautyCustomColor)
                                            {
                                                <div
                                                    class="absolute -top-0.5 -right-0.5 w-3 h-3 bg-pink-500 rounded-full flex items-center justify-center shadow-sm">
                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                         class="h-1.5 w-1.5 text-white" fill="none" viewBox="0 0 24 24"
                                                         stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                              stroke-width="3" d="M5 13l4 4L19 7"/>
                                                    </svg>
                                                </div>
                                            }
                                        </div>

                                        <!-- Skin texture preview -->
                                        <div class="relative mb-0.5">
                                            <img width="28" src="@(Dolo.Assets.MSPSkinPad)" alt="Skin preview"
                                                 class="drop-shadow-sm"/>
                                        </div>

                                        <!-- Color label: subtle, always visible -->
                                        <div
                                            class="bg-white/90 backdrop-blur-sm rounded px-1 py-0.5 transition-all duration-200 border border-gray-200">
                                            <p class="text-gray-700 text-[9px] font-medium">@item.Color</p>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Controls sidebar: added h-full to match left column height -->
                <div class="relative flex flex-col space-y-1.5 h-full">
                    <!-- Color settings card -->
                    <div class="bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200 p-2.5">
                        <div class="flex items-center justify-between mb-1.5">
                            <div>
                                <h3 class="text-gray-800 text-sm font-semibold">Color Settings</h3>
                                <p class="text-gray-600 text-xs">Custom mode</p>
                            </div>

                            <!-- Toggle switch -->
                            <div class="flex items-center space-x-2">
                                <span class="text-gray-600 text-xs font-medium">Custom</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input @onchange="e => SetCustomWithColor(Convert.ToBoolean(e.Value))"
                                           type="checkbox" class="sr-only peer"/>
                                    <div
                                        class="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-4 peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-0.5 after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all duration-300 peer-checked:bg-gradient-to-r peer-checked:from-pink-500 peer-checked:to-purple-600 border border-gray-300 peer-checked:border-pink-500"></div>
                                </label>
                                <span
                                    class="text-gray-800 text-xs font-medium">@(BeautyService.IsBeautyCustomColor ? "On" : "Off")</span>
                            </div>
                        </div>

                        <!-- Current color display -->
                        <div class="bg-gray-50 rounded-lg p-1.5 border border-gray-200">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600 text-xs">Current</span>
                                <div class="flex items-center space-x-2">
                                    <div
                                        class="w-3 h-3 rounded-full bg-[@(string.IsNullOrEmpty(BeautyService.SelectedBeautyColor) ? "#b52626" : BeautyService.IsBeautyCustomColor ? string.IsNullOrEmpty(BeautyService.SelectedBeautyCustomColor) ? "#b52626" : BeautyService.SelectedBeautyCustomColor : BeautyService.SelectedBeautyColor)] border border-gray-300 shadow-sm"></div>
                                    <span
                                        class="text-gray-700 font-mono text-xs">@(string.IsNullOrEmpty(BeautyService.SelectedBeautyColor) ? "#b52626" : BeautyService.IsBeautyCustomColor ? string.IsNullOrEmpty(BeautyService.SelectedBeautyCustomColor) ? "#b52626" : BeautyService.SelectedBeautyCustomColor : BeautyService.SelectedBeautyColor)</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preview section: min-h-[100px] for consistency -->
                    <div
                        class="bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200 p-2.5 min-h-[100px]">
                        <h3 class="text-gray-800 text-sm font-semibold mb-1.5">Live Preview</h3>
                        <div
                            class="bg-gray-50 rounded-lg p-2.5 border border-gray-200 flex justify-center items-center">
                            <div class="transform hover:scale-105 transition-transform duration-300 scale-75">
                                <BeautyHeadBoy IsVisible="@(LoginService.MspUser.Actor.Gender == Gender.Male)"
                                               @ref="BeautyService.BeautyHeadBoy"/>
                                <BeautyHeadGirl IsVisible="@(LoginService.MspUser.Actor.Gender == Gender.Female)"
                                                @ref="BeautyService.BeautyHeadGirl"/>
                            </div>
                        </div>
                    </div>

                    <!-- Color picker -->
                    <div class="bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200 p-2.5">
                        <input @oninput="e => SetSelectedColor(e.Value?.ToString())"
                               @onchange="e => SetSelectedColor(e.Value?.ToString())"
                               id="select_custom_color" type="color" class="sr-only peer">
                        <label for="select_custom_color"
                               class="@(!BeautyService.IsBeautyCustomColor ? "opacity-40 pointer-events-none" : "hover:bg-gray-50") group w-full flex items-center justify-center gap-2 cursor-pointer bg-gray-50 rounded-lg px-3 py-2 text-gray-800 transition-all duration-200 border border-gray-200 hover:border-gray-300">
                            <div
                                class="w-5 h-5 bg-gradient-to-br from-pink-500 to-purple-600 rounded flex items-center justify-center shadow-sm group-hover:scale-110 transition-transform duration-200">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-white" fill="none"
                                     viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 3V1m0 18v2m8-10h-1m-4 0H9m11-4V3a2 2 0 00-2-2h-4a2 2 0 00-2 2v4a2 2 0 002 2h4a2 2 0 002-2z"/>
                                </svg>
                            </div>
                            <div class="text-left">
                                <p class="font-medium text-sm">Color Picker</p>
                            </div>
                            <svg xmlns="http://www.w3.org/2000/svg"
                                 class="h-4 w-4 text-gray-400 group-hover:text-gray-600 group-hover:translate-x-0.5 transition-all duration-200"
                                 fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </label>
                    </div>

                    <!-- Purchase button -->
                    <Button IsFullHeight="false" OnClick="BeautyService.AskForBuyingBeauty" Name="Purchase Beauty"
                            Class="w-full text-white font-semibold py-2 rounded-lg transition-all duration-300"/>
                </div>
            </div>
        </div>
    </div>
</div>

@code {

    // Code logic remains unchanged; height fixes are in markup.
    protected override void OnAfterRender(bool firstRender)
    {
        if (!firstRender) return;

        SetCustomWithColor(BeautyService.IsBeautyCustomColor);
    }

    protected override void OnInitialized()
    {
        DialogService.BeautyShop = this;
    }

    private bool _isShown;

    public void Show()
    {
        _isShown = true;
        StateHasChanged();
    }

    public void Hide()
    {
        _isShown = false;
        StateHasChanged();
    }

    private void SetCustomWithColor(bool isCustom)
    {
        BeautyService.IsBeautyCustomColor = isCustom;
        SetSelectedColor(isCustom ? BeautyService.SelectedBeautyCustomColor : BeautyService.SelectedBeautyColor);
    }

    private void SetSelectedColor(string? color)
    {
        if (BeautyService.IsBeautyCustomColor)
            BeautyService.SelectedBeautyCustomColor = color;
        else
            BeautyService.SelectedBeautyColor = color;

        if (LoginService.MspUser.Actor.Gender == Gender.Male)
            BeautyService.BeautyHeadBoy?.SetColor(color);
        else
            BeautyService.BeautyHeadGirl?.SetColor(color);
    }

    private class BeautyShopItem(string color)
    {
        public string Color { get; } = color;
    }

    private readonly List<BeautyShopItem> _items =
    [
        new("#984F2B"),
        new("#855233"),
        new("#874637"),
        new("#B49174"),
        new("#9B7E67"),
        new("#B46F4F"),
        new("#DBA277"),
        new("#F3CDB0"),
        new("#D9A985"),
        new("#412000"),
        new("#562B00"),
        new("#663300"),
        new("#804622"),
        new("#8A5522"),
        new("#BD7139"),
        new("#CB824E"),
        new("#DF9B68"),
        new("#F3AB68"),
        new("#EFB288"),
        new("#EEC27E"),
        new("#FFCC99"),
        new("#FEDCAB"),
        new("#F4DDAC"),
        new("#FAD4C6"),
        new("#E5C89B"),
        new("#FFE0CC"),
        new("#FEEABC"),
        new("#FCE9D5"),
        new("#FFF4EE"),
        new("#DB824A"),
        new("#934621"),
        new("#FFCC9A"),
        new("#FFB99F"),
        new("#F7A471"),
        new("#EEDDDD"),
        new("#FFDBBD"),
        new("#FEF7E7"),
        new("#F3B67D"),
        new("#EBE8CF"),
        new("#F8DBB1"),
        new("#E6E3DD"),
        new("#FBEAF8"),
        new("#ECF8E4"),
        new("#DFECE7"),
        new("#F3D3B1"),
        new("#D8D8AF"),
        new("#FFDDDD"),
        new("#DDBEEB"),
        new("#F99FDE"),
        new("#DA9974"),
        new("#F2DEF5"),
        new("#333333"),
        new("#1E4A8C"),
        new("#915F41"),
        new("#93715B"),
        new("#FBE8CB"),
        new("#DDDDDD"),
        new("#B1775F"),
        new("#C1E2F0"),
        new("#DDEEFF"),
        new("#EEB88B"),
        new("#E6E6FF"),
        new("#D89C70")
    ];

}