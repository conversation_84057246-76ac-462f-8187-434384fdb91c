﻿using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class StatsService(
    LoginService loginService,
    DialogService dialogService) : IService
{
    public Stats? Stats { get; set; }

    public async Task UpdateAsync()
    {
        if (loginService.MspClient is null || !loginService.MspUser.LoggedIn) return;

        await dialogService.ShowLoaderAsync();

        var piggy = await loginService.MspClient.GetPiggyBankAsync();
        if (piggy.Success)
        {
            loginService.MspUser.Actor.PiggyBank = piggy;
            Stats?.StateHasChangedAsync();
        }

        var details = await loginService.MspClient.LoadActorDetailsExtendedAsync();
        if (!details.Success)
        {
            await dialogService.HideLoaderAsync();
            await dialogService.ShowToastAsync(a =>
                a.SetText("Fetching statistic does not worked ..").SetType(ToastType.Error).UseNewTask());
            return;
        }

        loginService.MspUser.Actor.UpdateStatistic(details);
        Stats?.StateHasChangedAsync();

        await dialogService.HideLoaderAsync();
        await dialogService.ShowToastAsync(a =>
            a.SetText("Statistics has been updated.").SetType(ToastType.Success).UseNewTask());
    }

    public void IncreaseStarCoins(int value)
    {
        loginService.MspUser.Actor.IncreaseStarCoin(value);
        Stats?.StateHasChangedAsync();
    }
}