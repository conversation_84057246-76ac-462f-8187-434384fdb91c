﻿using Dolo.Planet.Entities.Status;
using Dolo.Planet.Enums;
using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class StatusService(DialogService dialogService, LoginService loginService) : IService
{
    public Status? Status { get; set; }
    public StatusEmote? StatusEmote { get; set; }
    public StatusPose? StatusPoseComponent { get; set; }
    public StatusColor? StatusColor { get; set; }
    public FigureAnimationType? StatusPose { get; set; }
    public Emoticon? StatusEmoteSelected { get; set; }
    public string? StatusText { get; set; }
    public bool IsEmoteOpen { get; set; }
    public bool IsPoseOpen { get; set; }
    public bool IsTextEditor { get; set; }
    public int EmotePageId { get; set; }

    /// <summary>
    ///     Get the emotes
    /// </summary>
    public List<Emoticon> GetEmotes()
    {
        return EmotePageId switch
        {
            0 => Planet.Entities.Status.StatusEmote.GetSpecialEmotes(),
            1 => Planet.Entities.Status.StatusEmote.GetSmiley(),
            2 => Planet.Entities.Status.StatusEmote.GetPinkOctopus(),
            3 => Planet.Entities.Status.StatusEmote.GetSquare(),
            4 => Planet.Entities.Status.StatusEmote.GetHeart(),
            5 => Planet.Entities.Status.StatusEmote.GetPartyDj(),
            6 => Planet.Entities.Status.StatusEmote.GetSanta(),
            _ => Planet.Entities.Status.StatusEmote.GetSpecialEmotes()
        };
    }

    /// <summary>
    ///     Get the emote page name
    /// </summary>
    public string GetEmotePageName()
    {
        return EmotePageId switch
        {
            0 => "Msp-Special",
            1 => "Msp-Smiley",
            2 => "Msp-Octopus",
            3 => "Msp-Square",
            4 => "Msp-Heart",
            5 => "Msp-Dj",
            6 => "Msp-Santa",
            _ => "Msp-Special"
        };
    }

    public async Task SetStatusAsync()
    {
        if (string.IsNullOrEmpty(StatusText))
        {
            await dialogService.ShowToastAsync("Status cannot be empty", ToastType.Error);
            return;
        }

        await dialogService.ShowLoaderAsync();
        var status = await loginService.MspUser.Actor.SetStatusAsync(a =>
        {
            a.Color = StatusColor.GetValueOrDefault();
            a.HasCustomFigure = true;
            a.Figure = StatusPose ?? (loginService.MspUser.Actor.Gender == Gender.Male
                ? FigureAnimationType.Boy
                : FigureAnimationType.Girl);
            a.WithText(StatusText);
        });

        if (!status.Success)
        {
            await dialogService.HideLoaderAsync();
            await dialogService.ShowToastAsync("Please try again, there was something wrong", ToastType.Error);
            return;
        }

        SetTextEditor(false);
        loginService.MspUser.Actor.Status.SetStatusText(StatusText);
        loginService.MspUser.Actor.Status.SetStatusColor(StatusColor.GetValueOrDefault());
        await dialogService.HideLoaderAsync();
        await dialogService.ShowToastAsync("Status has been updated.", ToastType.Success);
    }

    /// <summary>
    ///     Set the emote and render the component
    /// </summary>
    public void SetEmote(Emoticon emote)
    {
        if (StatusEmoteSelected?.Name != emote.Name)
        {
            StatusEmoteSelected = emote;
            Status?.StateHasChangedAsync();
        }
    }

    /// <summary>
    ///     Set the pose and render the component
    /// </summary>
    public void SetPose(FigureAnimationType type)
    {
        StatusPose = type;
        HidePose();
    }

    /// <summary>
    ///     Hide the emote and render the component
    /// </summary>
    public void HideEmote()
    {
        IsEmoteOpen = false;
        Status?.StateHasChangedAsync();
    }

    /// <summary>
    ///     Set the pose and render the component
    /// </summary>
    public void HidePose()
    {
        IsPoseOpen = false;
        Status?.StateHasChangedAsync();
    }

    /// <summary>
    ///     Set the status color
    /// </summary>
    public void SetStatusColor(StatusColor color)
    {
        if (StatusColor != color)
        {
            loginService.MspUser.Actor.Status.SetStatusColor(color);
            StatusColor = color;
            Status?.StateHasChangedAsync();
        }
    }

    /// <summary>
    ///     Set the status text
    /// </summary>
    public void SetStatusText(string? text)
    {
        StatusText += text;
        Status?.StateHasChangedAsync();
    }

    /// <summary>
    ///     Set the text editor to true
    /// </summary>
    public void SetTextEditor(bool value)
    {
        IsTextEditor = value;
        Status?.StateHasChangedAsync();
    }
}