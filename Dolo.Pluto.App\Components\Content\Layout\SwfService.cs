﻿using Dolo.Core;
using Dolo.Pluto.Shard.Services;
using Microsoft.JSInterop;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class SwfService(IJSRuntime jsRuntime) : IService
{
    /// <summary>
    ///     Try to load a swf file with ruffle (optimized for performance)
    /// </summary>
    public async Task TryLoadSwfAsync<T>(DotNetObjectReference<T> reference, string? element, int width, int height,
        string? url, string? customClass = null, bool isFullSize = true) where T : class
    {
        try
        {
            Console.WriteLine($"SwfService: Loading SWF {url} into element {element} (isFullSize: {isFullSize})");

            // Use ConfigureAwait(false) for better async performance
            await jsRuntime
                .InvokeVoidAsync("LoadSWFFile", reference, element, width, height, url, customClass, isFullSize)
                .ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading SWF file: {ex.Message}");
        }
    }

    /// <summary>
    ///     Try to create a swf player
    /// </summary>
    public async Task TryCreatePlayerAsync(string? element)
    {
        try
        {
            await jsRuntime.InvokeVoidAsync("CreateRufflePlayer", element);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating Ruffle player: {ex.Message}");
        }
    }

    /// <summary>
    ///     Try to play a swf file with ruffle
    /// </summary>
    public async Task TryPlaySwfAsync(string? element)
    {
        await jsRuntime.TryInvokeVoidAsync("PlaySWFFile", element);
    }

    /// <summary>
    ///     Try to destroy a swf file with ruffle
    /// </summary>
    public async Task TryDestroySwfAsync(string? element)
    {
        await jsRuntime.TryInvokeVoidAsync("DestroySWFFile", element);
    }


    /// <summary>
    ///     Try to play a swf file with ruffle
    /// </summary>
    public async Task TryStopSwfAsync(string? element)
    {
        await jsRuntime.TryInvokeVoidAsync("StopSWFFile", element);
    }

    /// <summary>
    ///     Try to has a ruffle player
    /// </summary>
    public async Task<bool> TryHasRuffleAsync(string? element)
    {
        return await jsRuntime.TryInvokeAsync<bool>("HasRufflePlayer", element);
    }

    /// <summary>
    ///     Try to get the swf playing state
    /// </summary>
    public async Task<bool> TryIsPlayingAsync(string? element)
    {
        return await jsRuntime.TryInvokeAsync<bool>("isPlaying", element);
    }
}