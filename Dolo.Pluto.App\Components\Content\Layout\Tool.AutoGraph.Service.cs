﻿using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class ToolAutoGraphService : IService
{
    private static ToolAutoGraphService? _toolAutoGraphService;
    private readonly DialogService _dialogService;
    private readonly LoginService _loginService;
    private CancellationTokenSource? _tokenSource;

    public ToolAutoGraphService(LoginService loginService, DialogService dialogService)
    {
        _loginService = loginService;
        _dialogService = dialogService;
        _toolAutoGraphService = this;
    }

    /// <summary>
    ///     Autograph tool.
    /// </summary>
    public Tool_AutoGraph? AutoGraph { get; set; }

    /// <summary>
    ///     Earned Fame.
    /// </summary>
    public int Fame { get; set; }

    /// <summary>
    ///     Autographs sent successfully.
    /// </summary>
    public int Sent { get; set; }

    /// <summary>
    ///     Autographs failed to send.
    /// </summary>
    public int Failed { get; set; }

    /// <summary>
    ///     Username that will receive the autograph.
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    ///     Remaining time to send autographs.
    /// </summary>
    public TimeSpan TimeSpan => _tokenSource is null || _tokenSource.IsCancellationRequested
        ? new TimeSpan(0)
        : _loginService.MspUser.Actor.RemainingAutograph;

    /// <summary>
    ///     Indicate if the tool is running.
    /// </summary>
    public bool IsRunning { get; set; }

    public async Task StartAsync()
    {
        if (IsRunning)
        {
            IsRunning = false;
            AutoGraph?.StateHasChangedAsync();
            await _tokenSource!.CancelAsync();
            return;
        }

        if (string.IsNullOrEmpty(Username))
        {
            await _dialogService.ShowToastAsync("Please enter a username.", ToastType.Error);
            return;
        }

        var user = await _loginService.MspClient!.GetActorIdAsync(Username);
        if (!user.IsAvailable)
        {
            await _dialogService.ShowToastAsync("Please provide an existing username.", ToastType.Error);
            return;
        }

        if (user.Id == _loginService.MspUser.Actor.Id)
        {
            await _dialogService.ShowToastAsync("You can't send an autograph to yourself.", ToastType.Error);
            return;
        }

        _tokenSource = new CancellationTokenSource();
        IsRunning = true;
        AutoGraph?.StateHasChangedAsync();

        while (!_tokenSource.IsCancellationRequested)
        {
            if (!_loginService.MspUser.Actor.CanSendAutograph)
            {
                await Task.Delay(1000);
                AutoGraph?.StateHasChangedAsync();
                continue;
            }

            var req = await _loginService.MspClient.SendAutographAsync(user.Id);
            if (req is { Success: true })
            {
                if (!req.HasSended)
                {
                    Failed++;
                    AutoGraph?.StateHasChangedAsync();
                    await Task.Delay(5000);
                    continue;
                }

                Sent++;
                Fame += req.Fame;
                AutoGraph?.StateHasChangedAsync();
                await _dialogService.ShowToastAsync($"Autograph sent to {user.Username}.", true, ToastType.Success);
                continue;
            }

            Failed++;
            AutoGraph?.StateHasChangedAsync();
        }

        IsRunning = false;
        AutoGraph?.StateHasChangedAsync();
        await _tokenSource.CancelAsync();
    }

    public static void Dispose()
    {
        if (_toolAutoGraphService is null) return;

        _toolAutoGraphService._tokenSource?.Cancel();
        _toolAutoGraphService.IsRunning = false;
        _toolAutoGraphService.Failed = 0;
        _toolAutoGraphService.Sent = 0;
        _toolAutoGraphService.Fame = 0;
        _toolAutoGraphService.Username = null;
    }
}