﻿using System.Diagnostics;
using System.IO.Compression;
using Dolo.Core;
using Dolo.Core.Http;
using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class ToolBundlerService(LoginService loginService, DialogService dialogService, ToolService toolService)
    : IService
{
    /// <summary>
    ///     The username of the user to bundle
    /// </summary>
    public string? BundleUsername { get; set; }

    /// <summary>
    ///     The progress of the bundling
    /// </summary>
    public string BundleProgress { get; set; } = "0 / 0";

    /// <summary>
    ///     The progress Text of the bundling
    /// </summary>
    public string BundleProgressText { get; set; } = "Progress";

    /// <summary>
    ///     Indicate if the bundling is in progress
    /// </summary>
    public bool IsBundling { get; set; }

    /// <summary>
    ///     Ask for bundling if the user hits yes,
    ///     the bundling will start
    /// </summary>
    public void AskForBundling()
    {
        dialogService.ShowPopup($"Do you really want to bundle every look from {BundleUsername}", async ()
            => await StartBundleAsync());
    }

    /// <summary>
    ///     Set the progress of the bundling
    /// </summary>
    /// <param name="text">th message</param>
    /// <param name="progress">the progress (x / x)</param>
    private void SetBundleProgress(string text, string progress)
    {
        BundleProgressText = text;
        BundleProgress = progress;
        toolService.Tool?.StateHasChangedAsync();
    }

    /// <summary>
    ///     Starts the bundling process
    /// </summary>
    private async Task StartBundleAsync()
    {
        if (string.IsNullOrEmpty(BundleUsername))
        {
            await dialogService.ShowToastAsync("Please specify a username", ToastType.Error);
            return;
        }

        await dialogService.ShowLoaderAsync();

        var user = await loginService.MspClient!.GetActorIdAsync(BundleUsername);
        if (!user.Success || !user.IsAvailable)
        {
            await dialogService.HideLoaderAsync();
            await dialogService.ShowToastAsync($"User {BundleUsername} does not exist", ToastType.Error);
            return;
        }

        var looksImages = new List<string>();
        var looks = await loginService.MspClient!.GetLooksAsync(user.Id, 500);
        if (!looks.Success)
        {
            await dialogService.HideLoaderAsync();
            await dialogService.ShowToastAsync("Failed to fetch looks", ToastType.Error);
            return;
        }

        if (!looks.Any())
        {
            await dialogService.HideLoaderAsync();
            await dialogService.ShowToastAsync("User does not have any looks", ToastType.Error);
            return;
        }

        await dialogService.HideLoaderAsync();

        IsBundling = true;
        SetBundleProgress("Downloading ..", "0 / 0");

        looksImages.AddRange(looks.Select(a => a.Url));

        var bundlePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), ".pluto",
            "pluto-bundle");
        var zipPath = Path.Combine(bundlePath, $"pluto-{user.Id}.zip");
        var plutoBundle = Path.Combine(bundlePath, $"pluto-bundle-{user.Id}");

        await TryIt.ThisAsync(async () =>
        {
            if (!Directory.Exists(bundlePath))
                Directory.CreateDirectory(bundlePath);

            if (!Directory.Exists(plutoBundle))
                Directory.CreateDirectory(plutoBundle);

            for (var i = 0; i < looksImages.Count; i++)
            {
                var stream = await Http.TryDownloadAsync(a => { a.Url = looksImages[i]; });

                if (stream.Stream is null)
                    continue;

                await using var fileStream = File.Create(Path.Combine(plutoBundle, looksImages[i].Split('/').Last()));
                await stream.Stream.CopyToAsync(fileStream);
                SetBundleProgress("Downloading ..", $"{i + 1:N0} / {looksImages.Count:N0}");
            }

            SetBundleProgress("Zipping ..", "");

            if (File.Exists(zipPath))
                File.Delete(zipPath);

            if (!Directory.EnumerateFiles(plutoBundle).Any())
            {
                IsBundling = false;
                SetBundleProgress("Progress", "0 / 0");

                await dialogService.ShowToastAsync("zipping failed during empty directory", ToastType.Error);
                return;
            }

            ZipFile.CreateFromDirectory(plutoBundle, zipPath);
            Directory.Delete(plutoBundle, true);
            Process.Start("explorer.exe", zipPath);

            IsBundling = false;
            SetBundleProgress("Progress", "0 / 0");

            await dialogService.ShowToastAsync("looks saved to disk", ToastType.Success);
        }, async exception =>
        {
            IsBundling = false;
            SetBundleProgress("Progress", "0 / 0");

            await dialogService.ShowToastAsync($"Failed to bundle looks, {exception.Message}", ToastType.Error);
        });
    }
}