﻿using Dolo.Planet.Chat;
using Dolo.Planet.Enums;
using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class ToolChatItService(LoginService loginService, DialogService dialogService, ToolService toolService)
    : IService
{
    private readonly DialogService _dialogService = dialogService;
    private readonly LoginService _loginService = loginService;
    private readonly ToolService _toolService = toolService;


    public MspChat? Chat { get; set; }
    public bool IsConnected { get; set; }
    public RoomType Room { get; set; }
}