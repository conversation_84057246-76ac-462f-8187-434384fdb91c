﻿using Dolo.Planet.Entities.Beauty;
using Dolo.Planet.Enums;
using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class ToolClinicService(LoginService loginService, DialogService dialogService) : IService
{
    /// <summary>
    ///     The type of the beauty
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    ///     The color of the beauty
    /// </summary>
    public string? Color { get; set; }

    /// <summary>
    ///     The Id of the beauty
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    ///     The BeautyData
    /// </summary>
    public MspBeautyData BeautyData { get; set; } = new();

    /// <summary>
    ///     Load Beauty Items
    /// </summary>
    public async Task LoadItemsAsync()
    {
        if (BeautyData.Mouths.Count != 0
            || BeautyData.Noses.Count != 0
            || BeautyData.Eyes.Count != 0
            || BeautyData.EyeShadows.Count != 0) return;

        BeautyData = await loginService.MspClient!.GetBeautyClinicAsync();
    }


    /// <summary>
    ///     Ask for a custom beauty to buy
    ///     if the user hits yes, the beauty will be bought
    /// </summary>
    public void AskForBeauty()
    {
        dialogService.ShowPopup($"Do you really want to buy {Type} with id {Id}", async ()
            => await BuyCustomBeautyAsync());
    }

    /// <summary>
    ///     Buy the beauty with the specified type, id and color
    /// </summary>
    private async Task BuyCustomBeautyAsync()
    {
        if (string.IsNullOrEmpty(Type))
        {
            await dialogService.ShowToastAsync("Please specify a type", ToastType.Error);
            return;
        }

        if (Id == 0)
        {
            await dialogService.ShowToastAsync("Please specify a id", ToastType.Error);
            return;
        }

        var req = await loginService.MspClient!.BuyBeautyClinicAsync(new MspBeautyItem(Type switch
            {
                "eyes" => BeautyType.Eyes,
                "eyeshadow" => BeautyType.EyeShadows,
                "nose" => BeautyType.Noses,
                "mouth" => BeautyType.Mouths,
                _ => BeautyType.Skins
            })
            .SetItem(Id)
            .SetColors(Color));

        if (!req.Success)
        {
            await dialogService.ShowToastAsync("Failed to buy beauty", ToastType.Error);
            return;
        }

        if (!req.Value?.HasSuccessfullyBought() ?? false)
        {
            await dialogService.ShowToastAsync("It's somehow not possible to buy this item", ToastType.Error);
            return;
        }

        await dialogService.ShowToastAsync("Successfully bought beauty", ToastType.Success);
    }
}