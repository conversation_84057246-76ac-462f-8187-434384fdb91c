﻿using Dolo.Planet.Entities.Cloth;
using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class ToolEasyGiftService : IService
{
    private static ToolEasyGiftService? _instance;
    private readonly DialogService _dialogService;
    private readonly LoginService _loginService;

    public ToolEasyGiftService(DialogService dialogService, LoginService loginService)
    {
        _dialogService = dialogService;
        _loginService = loginService;
        _instance = this;
    }

    public Tool_EasyGift? EasyGift { get; set; }
    public bool IsItemMenuOpen { get; set; }
    public string? Username { get; set; }
    public List<MspClothRel> Items { get; set; } = new();
    public MspClothRel? SelectedItem { get; set; }

    public int ItemIndex
    {
        get { return SelectedItem != null ? Items.FindIndex(a => a.Id == SelectedItem.Id) : 0; }
        set { }
    }

    public bool IsFetching { get; set; }
    public bool IsLoaded { get; set; }

    public async Task LoadAsync(bool isUpdate = false)
    {
        if (Items.Count != 0 && !isUpdate) return;

        IsFetching = true;
        EasyGift?.StateHasChangedAsync();

        var inventory = await _loginService.MspClient!.LoadActorGiftsAsync();
        if (!inventory.Success)
        {
            IsFetching = false;
            EasyGift?.StateHasChangedAsync();

            await _dialogService.ShowToastAsync("Failed to load items try again ..", ToastType.Error);
            return;
        }

        Items = inventory.ToList();
        IsFetching = false;
        EasyGift?.StateHasChangedAsync();
    }

    public async Task SendAsync()
    {
        if (string.IsNullOrEmpty(Username))
        {
            await _dialogService.ShowToastAsync("Please provide an username", ToastType.Error);
            return;
        }

        if (SelectedItem is null)
        {
            await _dialogService.ShowToastAsync("Please select an Item", ToastType.Error);
            return;
        }

        await _dialogService.ShowLoaderAsync();
        var hasLimit = await _loginService.MspClient!.HasGiftLimitAsync();
        if (hasLimit.Value)
        {
            await _dialogService.HideLoaderAsync();
            await _dialogService.ShowToastAsync("You reached the daily gift limit", ToastType.Error);
            return;
        }

        var user = await _loginService.MspClient!.GetActorIdAsync(Username);
        if (!user.IsAvailable)
        {
            await _dialogService.HideLoaderAsync();
            await _dialogService.ShowToastAsync("Please specify a valid username, the user is not available",
                ToastType.Error);
            return;
        }

        if (user.Id == _loginService.MspClient!.User.Actor.Id)
        {
            await _dialogService.HideLoaderAsync();
            await _dialogService.ShowToastAsync("You can't send a gift to yourself", ToastType.Error);
            return;
        }

        var item = SelectedItem.Id;
        var usrI = user.Id;

        var result = await _loginService.MspClient.GiveGiftAsync(usrI, item);
        if (!result.HasGiftedSuccessfully)
        {
            await _dialogService.HideLoaderAsync();
            await _dialogService.ShowToastAsync($"Failed to send the gift {result.Description}", ToastType.Error);
            return;
        }

        Items.Remove(SelectedItem);
        EasyGift?.StateHasChangedAsync();

        await _dialogService.HideLoaderAsync();
        await _dialogService.ShowToastAsync("Gift has been sent to the user", ToastType.Success);
    }

    public static void Dispose()
    {
        if (_instance is null) return;

        _instance.Items.Clear();
        _instance.SelectedItem = null;
        _instance.Username = null;
    }
}