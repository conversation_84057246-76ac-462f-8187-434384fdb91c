﻿using Dolo.Planet.Entities.Cloth;
using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class ToolEasyRecycleService : IService
{
    private static ToolEasyRecycleService? _instance;
    private readonly DialogService _dialogService;
    private readonly LoginService _loginService;

    public ToolEasyRecycleService(DialogService dialogService, LoginService loginService)
    {
        _dialogService = dialogService;
        _loginService = loginService;
        _instance = this;
    }

    public Tool_EasyRecycle? EasyRecycle { get; set; }
    public bool IsItemMenuOpen { get; set; }
    public List<MspClothRel> Items { get; set; } = new();
    public MspClothRel? SelectedItem { get; set; }

    public int ItemIndex
    {
        get { return SelectedItem != null ? Items.FindIndex(a => a.Id == SelectedItem.Id) : 0; }
        set { }
    }

    public bool IsFetching { get; set; }
    public bool IsLoaded { get; set; }

    public async Task LoadAsync(bool isUpdate = false)
    {
        if (Items.Count != 0 && !isUpdate)
            return;

        IsFetching = true;
        EasyRecycle?.StateHasChangedAsync();

        var inventory = await _loginService.MspClient!.LoadActorGiftsAsync();
        if (!inventory.Success)
        {
            IsFetching = false;
            EasyRecycle?.StateHasChangedAsync();

            await _dialogService.ShowToastAsync("Failed to load items try again ..", ToastType.Error);
            return;
        }

        Items = inventory.ToList();
        IsFetching = false;
        EasyRecycle?.StateHasChangedAsync();
    }

    public async Task RecycleAsync()
    {
        if (SelectedItem is null)
        {
            await _dialogService.ShowToastAsync("Please select an Item", ToastType.Error);
            return;
        }

        await _dialogService.ShowLoaderAsync();

        var item = Convert.ToInt32(SelectedItem.Id);
        var result = await _loginService.MspClient!.RecycleItemAsync(item);

        if (!result.Success || result.Value is 0)
        {
            await _dialogService.HideLoaderAsync();
            await _dialogService.ShowToastAsync("Failed to recycle item, make sure you dont wear them",
                ToastType.Error);
            return;
        }

        Items.Remove(SelectedItem);
        EasyRecycle?.StateHasChangedAsync();

        await _dialogService.HideLoaderAsync();
        await _dialogService.ShowToastAsync("Item has been recycled", ToastType.Success);
    }

    public static void Dispose()
    {
        if (_instance is null) return;

        _instance.Items.Clear();
        _instance.SelectedItem = null;
    }
}