﻿using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class ToolGreetItService : IService
{
    private static ToolGreetItService? _instance;
    private readonly DialogService _dialogService;
    private readonly LoginService _loginService;

    public ToolGreetItService(LoginService loginService, DialogService dialogService)
    {
        _loginService = loginService;
        _dialogService = dialogService;
        _instance = this;
    }

    public Tool_GreetIt? GreetIt { get; set; }
    public string? Username { get; set; }
    public int Sent { get; set; }
    public int Failed { get; set; }
    public int Amount { get; set; } = 1;

    public void AskForSendingGreet()
    {
        _dialogService.ShowPopup($"Do you really want to send {Amount} greet to {Username}?", SendGreetAsync);
    }

    private async Task SendGreetAsync()
    {
        if (string.IsNullOrEmpty(Username))
        {
            await _dialogService.ShowToastAsync("Please specify a username", ToastType.Error);
            return;
        }

        await _dialogService.ShowLoaderAsync();
        var user = await _loginService.MspClient!.GetActorIdAsync(Username);
        if (!user.IsAvailable)
        {
            await _dialogService.HideLoaderAsync();
            await _dialogService.ShowToastAsync("Please specify a valid username, the user is not available",
                ToastType.Error);
            return;
        }

        var lastError = string.Empty;
        var tmpSent = Sent;
        for (var i = 0; i < Amount; i++)
        {
            var result = await _loginService.MspClient.BuyGreetingAsync(user.Id);
            if (!result.IsSuccessStatusCode)
            {
                Failed++;
                GreetIt?.StateHasChangedAsync();
                lastError = result.Description;

                if (result.Code is -1 or -2)
                    break;

                continue;
            }

            Sent++;
            GreetIt?.StateHasChangedAsync();
        }

        await _dialogService.HideLoaderAsync();

        if (Sent != tmpSent)
            await _dialogService.ShowToastAsync(a =>
                a.SetText($"{tmpSent} Greet sent to {Username}").SetType(ToastType.Success).UseNewTask());

        if (!string.IsNullOrEmpty(lastError))
            await _dialogService.ShowToastAsync(new ToastContent()
                .SetText($"There were some mistakes: {lastError}")
                .SetType(ToastType.Warning)
                .SetDuration(TimeSpan.FromSeconds(5)));
    }


    public static void Dispose()
    {
        if (_instance is null) return;

        _instance.Username = string.Empty;
        _instance.Sent = 0;
    }
}