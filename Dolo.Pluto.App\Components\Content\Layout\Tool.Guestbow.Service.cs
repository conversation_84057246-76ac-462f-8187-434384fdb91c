﻿using Dolo.Planet.Entities.Status;
using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class ToolGuestbowService(LoginService loginService, DialogService dialogService) : IService
{
    /// <summary>
    ///     The username of the user to send the guestbook entry to
    /// </summary>
    public string? Username { get; set; }

    /// <summary>
    ///     The message of the guestbook entry
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    ///     The color of the guestbook entry
    /// </summary>
    public StatusColor Color { get; set; }

    /// <summary>
    ///     Send a guestbook entry to the user
    /// </summary>
    public async Task SendGuestbowAsync()
    {
        if (string.IsNullOrEmpty(Message))
        {
            await dialogService.ShowToastAsync("Please enter a message first.", ToastType.Error);
            return;
        }

        if (string.IsNullOrEmpty(Username))
        {
            await dialogService.ShowToastAsync("Please enter a username first.", ToastType.Error);
            return;
        }

        await dialogService.ShowLoaderAsync();
        var actor = await loginService.MspClient!.GetActorIdAsync(Username!);
        if (!actor.IsAvailable)
        {
            await dialogService.HideLoaderAsync();
            await dialogService.ShowToastAsync("User not found.", ToastType.Error);
            return;
        }

        var post = await loginService.MspClient!.PostGuestbookAsync(Message, actor.Id, Color);
        if (!post.Success)
        {
            await dialogService.HideLoaderAsync();
            await dialogService.ShowToastAsync($"Entry could not be sent {post.Status}.", ToastType.Error);
            return;
        }

        await dialogService.HideLoaderAsync();
        await dialogService.ShowToastAsync("Guestbook entry has been sent.", ToastType.Success);
    }
}