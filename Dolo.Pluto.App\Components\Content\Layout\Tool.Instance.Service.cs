﻿using System.Diagnostics;
using System.Security.Cryptography;
using System.Xml;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class ToolInstanceService(DialogService dialogService, IReportService reportService) : IService
{
    /// <summary>
    ///     The app path of the moviestarplanet installation
    /// </summary>
    private static string AppPath => "c:\\Games\\MovieStarPlanet\\";

    /// <summary>
    ///     The app xaml file to manipulate
    /// </summary>
    private static string AppXaml => Path.Combine(AppPath + "META-INF", "AIR", "application.xml");

    /// <summary>
    ///     The ml file to launch
    /// </summary>
    private static string MlFile => Path.Combine(AppPath, "ml.exe");

    /// <summary>
    ///     Manipulate the app-xaml file to create a new instance
    /// </summary>
    private static Task ManipulateAppXamlAsync()
    {
        return Task.Run(() =>
        {
            try
            {
                var xml = new XmlDocument();
                xml.Load(AppXaml);

                var idElements = xml.GetElementsByTagName("id");
                if (idElements.Count == 0)
                    throw new InvalidOperationException("No 'id' element found in application.xml");

                var id = idElements[0];
                if (id == null)
                    throw new InvalidOperationException("'id' element is null in application.xml");

                id.InnerText = RandomNumberGenerator.GetInt32(0, 10000).ToString();
                xml.Save(AppXaml);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to manipulate application.xml: {ex.Message}", ex);
            }
        });
    }

    /// <summary>
    ///     Launch the ml.exe file
    /// </summary>
    private static Task LaunchMovieStarPlanetAsync()
    {
        return Task.Run(() =>
        {
            try
            {
                if (!File.Exists(MlFile))
                    throw new FileNotFoundException($"MovieStarPlanet executable not found at: {MlFile}");

                var process = new Process();
                process.StartInfo.FileName = MlFile;
                process.StartInfo.WorkingDirectory = AppPath;

                if (!process.Start())
                    throw new InvalidOperationException("Failed to start MovieStarPlanet process");
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to launch MovieStarPlanet: {ex.Message}", ex);
            }
        });
    }

    /// <summary>
    ///     Spawn a new instance of moviestarplanet
    /// </summary>
    public async Task SpawnInstanceAsync()
    {
        try
        {
            // Check if installation exists
            if (!Directory.Exists(AppPath))
            {
                await ShowErrorPopupAsync("Installation Not Found",
                    "MovieStarPlanet installation directory not found. Please install MovieStarPlanet first.");
                return;
            }

            if (!File.Exists(AppXaml))
            {
                await ShowErrorPopupAsync("Configuration Missing",
                    "MovieStarPlanet application.xml file not found. Please reinstall MovieStarPlanet.");
                return;
            }

            if (!File.Exists(MlFile))
            {
                await ShowErrorPopupAsync("Executable Missing",
                    "MovieStarPlanet executable (ml.exe) not found. Please reinstall MovieStarPlanet.");
                return;
            }

            await dialogService.ShowLoaderAsync();

            // Create instances with enhanced error handling
            for (var i = 0; i < 2; i++)
                try
                {
                    await ManipulateAppXamlAsync();
                    await Task.Delay(1000);
                    await LaunchMovieStarPlanetAsync();
                    await Task.Delay(1000);
                }
                catch (Exception ex)
                {
                    await dialogService.HideLoaderAsync();

                    var errorMessage = $"Failed to create instance {i + 1}: {ex.Message}";
                    await reportService.ReportErrorAsync(errorMessage, "ToolInstanceService.SpawnInstanceAsync", ex);

                    await ShowErrorPopupAsync("Instance Creation Failed",
                        $"Failed to create MovieStarPlanet instance {i + 1}. The error has been reported automatically.");
                    return;
                }

            await dialogService.HideLoaderAsync();
            await ShowSuccessPopupAsync("Success!",
                "Two new MovieStarPlanet instances have been created successfully.");
        }
        catch (Exception ex)
        {
            await dialogService.HideLoaderAsync();

            var errorMessage = "Unexpected error in SpawnInstanceAsync";
            await reportService.ReportErrorAsync(errorMessage, "ToolInstanceService.SpawnInstanceAsync", ex);

            await ShowErrorPopupAsync("Unexpected Error",
                "An unexpected error occurred while creating instances. The error has been reported automatically.");
        }
    }

    /// <summary>
    ///     Show error popup with consistent styling
    /// </summary>
    private async Task ShowErrorPopupAsync(string title, string message)
    {
        await dialogService.ShowPopupAsync(options =>
        {
            options.SetTitle(title)
                .SetMessage(message)
                .SetButtonText("Understood")
                .SetButtonAction(() =>
                {
                    /* Close popup */
                })
                .AllowClose();
        });
    }

    /// <summary>
    ///     Show success popup with consistent styling
    /// </summary>
    private async Task ShowSuccessPopupAsync(string title, string message)
    {
        await dialogService.ShowPopupAsync(options =>
        {
            options.SetTitle(title)
                .SetMessage(message)
                .SetButtonText("Great!")
                .SetButtonAction(() =>
                {
                    /* Close popup */
                })
                .AllowClose();
        });
    }
}