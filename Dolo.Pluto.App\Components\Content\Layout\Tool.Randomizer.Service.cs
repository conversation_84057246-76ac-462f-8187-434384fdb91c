﻿using Dolo.Planet.Entities;
using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class ToolRandomizerService(LoginService loginService, DialogService dialogService, ToolService toolService)
    : IService
{
    /// <summary>
    ///     Look info to display it in the tool
    /// </summary>
    public List<MspActorCloth> LookInfo { get; set; } = new();

    /// <summary>
    ///     Ask if the user really wants to change his look
    /// </summary>
    public void AskForRandomLook()
    {
        dialogService.ShowPopup("Plut<PERSON> will change your outfit completely, do you really want that?",
            GetRandomLookAsync);
    }

    /// <summary>
    ///     Get a random look and set it to the user
    /// </summary>
    private async Task GetRandomLookAsync()
    {
        await dialogService.ShowLoaderAsync();
        var look = await loginService.MspUser.Actor.GenerateLookAsync();
        if (!look.Any())
        {
            await dialogService.HideLoaderAsync();
            await dialogService.ShowToastAsync("No look found.", ToastType.Error);
            return;
        }

        LookInfo = look;
        toolService.Tool?.StateHasChangedAsync();

        await dialogService.HideLoaderAsync();
        await dialogService.ShowToastAsync("Look has been generated. restart msp", ToastType.Success);
    }
}