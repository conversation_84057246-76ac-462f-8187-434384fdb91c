﻿using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class ToolRemoverService(LoginService loginService, DialogService dialogService) : IService
{
    /// <summary>
    ///     When the look is from someone else
    /// </summary>
    public bool IsLookFromOther { get; set; }

    /// <summary>
    ///     All options for the remover
    /// </summary>
    public string? Option { get; set; }

    /// <summary>
    ///     Method to ask for removing if the
    ///     user hits yes, the removing will start
    /// </summary>
    public void AskForRemoving()
    {
        dialogService.ShowPopup($"Do you really want to delete every {Option}", async ()
            => await StartRemovingAsync());
    }

    /// <summary>
    ///     Method to start removing
    /// </summary>
    private async Task StartRemovingAsync()
    {
        if (string.IsNullOrEmpty(Option))
        {
            await dialogService.ShowToastAsync("Please select an option first.", ToastType.Error);
            return;
        }

        await dialogService.ShowLoaderAsync();
        switch (Option)
        {
            case "look":
                await TryRemoveLooksAsync();
                return;
            case "artbook":
                await TryRemovArtbooksAsync();
                return;
            case "movie":
                await TryRemoveMoviesAsync();
                return;
            case "guestbook":
                await TryRemoveGuestbooksAsync();
                return;
            case "status":
                await TryRemoveStatusesAsync();
                return;
        }
    }

    /// <summary>
    ///     Tries to remove the status posts
    /// </summary>
    private async Task TryRemoveStatusesAsync()
    {
        var status = await loginService.MspClient!.GetWallActivitiesAsync(0, 500);
        if (!status.Any())
        {
            await dialogService.HideLoaderAsync();
            await dialogService.ShowToastAsync("You have no status updates.", ToastType.Error);
            return;
        }

        foreach (var entry in status) await entry.DeleteAsync();

        await dialogService.HideLoaderAsync();
        await dialogService.ShowToastAsync("All status updates has been removed.", ToastType.Success);
    }

    /// <summary>
    ///     Tries to remove the guestbooks
    /// </summary>
    private async Task TryRemoveGuestbooksAsync()
    {
        var guestbook = await loginService.MspClient!.GetWallPostsAsync();
        if (!guestbook.Any())
        {
            await dialogService.HideLoaderAsync();
            await dialogService.ShowToastAsync("You have no guestbook entries.", ToastType.Error);
            return;
        }

        foreach (var entry in guestbook) await entry.DeleteAsync();

        await dialogService.HideLoaderAsync();
        await dialogService.ShowToastAsync("All guestbook entries has been removed.", ToastType.Success);
    }

    /// <summary>
    ///     Tries to remove the movies
    /// </summary>
    private async Task TryRemoveMoviesAsync()
    {
        var movies = await loginService.MspClient!.GetMoviesAsync();
        if (!movies.Any())
        {
            await dialogService.HideLoaderAsync();
            await dialogService.ShowToastAsync("You have no movies.", ToastType.Error);
            return;
        }

        foreach (var movie in movies) await movie.DeleteAsync();

        await dialogService.HideLoaderAsync();
        await dialogService.ShowToastAsync("All movies has been removed.", ToastType.Success);
    }

    /// <summary>
    ///     Tries to remove the looks
    /// </summary>
    private async Task TryRemoveLooksAsync()
    {
        var looks = await loginService.MspClient!.GetLooksAsync();
        if (!looks.Any())
        {
            await dialogService.HideLoaderAsync();
            await dialogService.ShowToastAsync("You have no looks.", ToastType.Error);
            return;
        }

        if (IsLookFromOther && looks.All(a => !a.IsFromOther))
        {
            await dialogService.HideLoaderAsync();
            await dialogService.ShowToastAsync("You have no looks from yourself.", ToastType.Error);
            return;
        }

        foreach (var look in IsLookFromOther ? looks.Where(a => a.IsFromOther) : looks) await look.DeleteAsync();

        await dialogService.HideLoaderAsync();
        await dialogService.ShowToastAsync(IsLookFromOther
            ? "All looks from others has been removed"
            : "All looks has been removed.", ToastType.Success);
    }

    /// <summary>
    ///     Tries to remove the artbooks
    /// </summary>
    private async Task TryRemovArtbooksAsync()
    {
        var artbooks = await loginService.MspClient!.GetArtbooksAsync();
        if (!artbooks.Any())
        {
            await dialogService.HideLoaderAsync();
            await dialogService.ShowToastAsync("You have no artbooks.", ToastType.Error);
            return;
        }

        foreach (var artbook in artbooks) await artbook.DeleteAsync();

        await dialogService.HideLoaderAsync();
        await dialogService.ShowToastAsync("All artbooks has been removed.", ToastType.Success);
    }
}