﻿using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class ToolSponsorService(LoginService loginService, DialogService dialogService) : IService
{
    /// <summary>
    ///     Get all the sponsors and accept them
    ///     only if the server has sponsors
    /// </summary>
    public async Task GetSponsorsAsync()
    {
        await dialogService.ShowLoaderAsync();
        var sponsors = await loginService.MspClient!.GetAnchorCharacterListAsync();
        if (!sponsors.Any())
        {
            await dialogService.HideLoaderAsync();
            await dialogService.ShowToastAsync("Your server has no sponsors.", ToastType.Error);
            return;
        }

        foreach (var sponsor in sponsors)
            await loginService.MspClient.AcceptFriendshipAsync(sponsor.ActorId);

        await dialogService.HideLoaderAsync();
        await dialogService.ShowToastAsync("You have now all sponsors and their items. look in your inventory",
            ToastType.Success);
    }
}