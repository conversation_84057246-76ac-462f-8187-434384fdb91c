﻿using Dolo.Planet.Entities;
using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class ToolWhoblockService : IService
{
    private readonly DialogService _dialogService;
    private readonly LoginService _loginService;
    private readonly ToolService _toolService;

    public ToolWhoblockService(LoginService loginService, DialogService dialogService, ToolService toolService)
    {
        _loginService = loginService;
        _dialogService = dialogService;
        _toolService = toolService;

        Instance = this;
        Blocks = new MspBlock();
    }

    /// <summary>
    ///     The instance of the service
    ///     used to dispose the blocks
    /// </summary>
    public static ToolWhoblockService? Instance { get; set; }

    /// <summary>
    ///     List of blocked users or users who blocked you
    /// </summary>
    public MspBlock Blocks { get; set; }

    /// <summary>
    ///     Gets the list of blocked users or users who blocked you
    /// </summary>
    public async Task GetBlockListAsync()
    {
        await _dialogService.ShowLoaderAsync();
        var block = await _loginService.MspClient!.GetBlockedListAsync();
        if (block.Blocking.Count is 0 && block.BlockedBy.Count is 0)
        {
            await _dialogService.HideLoaderAsync();
            await _dialogService.ShowToastAsync("You dont have any blocking or blocked users.", ToastType.Success);
            return;
        }

        if (!block.Success)
        {
            await _dialogService.HideLoaderAsync();
            await _dialogService.ShowToastAsync("Interaction to msp failed", ToastType.Error);
            return;
        }

        Blocks = block;
        _toolService.Tool?.StateHasChangedAsync();

        await _dialogService.HideLoaderAsync();
    }

    public static void Dispose()
    {
        if (Instance is null) return;

        Instance.Blocks.Blocking.Clear();
        Instance.Blocks.BlockedBy.Clear();
    }
}