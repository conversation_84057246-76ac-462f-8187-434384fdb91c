﻿using Dolo.Planet.Entities;
using Dolo.Pluto.App.Components.Content.Pages;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class ToolWishAddService : IService
{
    private static ToolWishAddService? _instance;
    private readonly DialogService _dialogService;
    private readonly LoginService _loginService;

    public ToolWishAddService(LoginService loginService, DialogService dialogService)
    {
        _loginService = loginService;
        _dialogService = dialogService;
        _instance = this;
    }

    public Tool_WishAdd? WishAdd { get; set; }
    public string? ItemId { get; set; }
    public string? Color { get; set; }

    public async Task AddToWishlistAsync()
    {
        if (!int.TryParse(ItemId, out var val))
        {
            await _dialogService.ShowToastAsync("Please specify an item", ToastType.Error);
            return;
        }

        await _dialogService.ShowLoaderAsync();
        var req = await _loginService.MspClient!.AddItemToWishlistAsync(new WishlistItem()
            .AddItem(val, Color));

        if (!req.Success)
        {
            await _dialogService.HideLoaderAsync();
            await _dialogService.ShowToastAsync("Failed to add item to wishlist", ToastType.Error);
            return;
        }

        if (req.Value == 12)
        {
            await _dialogService.HideLoaderAsync();
            await _dialogService.ShowToastAsync("You reached the maximum amount of items in your wishlist",
                ToastType.Error);
            return;
        }

        if (req.Value != 0)
        {
            await _dialogService.HideLoaderAsync();
            await _dialogService.ShowToastAsync("Failed to add item to wishlist", ToastType.Error);
            return;
        }


        await _dialogService.HideLoaderAsync();
        await _dialogService.ShowToastAsync("Item added to wishlist", ToastType.Success);
    }
}