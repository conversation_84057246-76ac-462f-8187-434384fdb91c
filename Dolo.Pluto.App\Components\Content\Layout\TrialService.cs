﻿using Dolo.Core;
using Dolo.Pluto.Shard.Services;
using Microsoft.Extensions.Logging;

namespace Dolo.Pluto.App.Components.Content.Layout;

public class TrialService(ILogger<TrialService>? logger = null) : IService
{
    private const string PrivateKey = "simple_trial_key_2025";
    private readonly ILogger<TrialService>? _logger = logger;

    private static string AppPath =>
        Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + "\\MSP\\Pluto\\";

    private static string AppTrial => Path.Combine(AppPath, "trial.dat");

    public async Task<TrialInfo> GetTrialInfoAsync()
    {
        try
        {
            if (!Directory.Exists(AppPath)) Directory.CreateDirectory(AppPath);

            if (!File.Exists(AppTrial))
            {
                var newTrial = new TrialInfo { Uses = 0, MaxUses = 20 };
                await SaveTrialAsync(newTrial);
                return newTrial;
            }

            var content = await File.ReadAllTextAsync(AppTrial);
            var trial = content.TryParse<TrialInfo>();

            return trial ?? new TrialInfo { Uses = 0, MaxUses = 20 };
        }
        catch
        {
            return new TrialInfo { Uses = 20, MaxUses = 20 }; // Return expired on any error
        }
    }

    public async Task<TrialInfo> AddTrialUseAsync()
    {
        try
        {
            var trial = await GetTrialInfoAsync();

            if (trial.Uses >= trial.MaxUses) return trial; // Already expired

            trial.Uses++;
            trial.LastUsed = DateTime.UtcNow;

            await SaveTrialAsync(trial);
            return trial;
        }
        catch
        {
            return new TrialInfo { Uses = 20, MaxUses = 20 }; // Return expired on any error
        }
    }

    public async Task<TrialInfo> ResetTrialAsync()
    {
        try
        {
            var newTrial = new TrialInfo { Uses = 0, MaxUses = 20 };
            await SaveTrialAsync(newTrial);
            return newTrial;
        }
        catch
        {
            return new TrialInfo { Uses = 20, MaxUses = 20 };
        }
    }

    public async Task<TrialInfo> ExpireTrialAsync()
    {
        try
        {
            var trial = await GetTrialInfoAsync();
            trial.Uses = trial.MaxUses; // Set to max to expire
            await SaveTrialAsync(trial);
            return trial;
        }
        catch
        {
            return new TrialInfo { Uses = 20, MaxUses = 20 };
        }
    }

    private async Task SaveTrialAsync(TrialInfo trial)
    {
        try
        {
            var json = trial.ToJson();
            await File.WriteAllTextAsync(AppTrial, json);
        }
        catch
        {
            // Ignore save errors
        }
    }
}