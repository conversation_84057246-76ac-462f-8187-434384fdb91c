﻿@page "/license"
@using Dolo.Core.Http
@using Dolo.Pluto.App.Components.Content.Layout
@using Dolo.Pluto.App.Core
@using System.Net
@using Dolo.Pluto.Shard.Services
@using Dolo.Pluto.Shard.Services.License
@using Dolo.Pluto.Shard.Hub
@using Dolo.Pluto.Shard
@inject DialogService DialogService
@inject NavigationManager NavigationManager
@inject ILicenseService LicenseService
@inject UrlBuilderService UrlBuilderService

@* @attribute [StreamRendering(true)] *@
<div class="flex h-full w-full bg-[url('image.png')] bg-cover items-center justify-center">
    <div class="absolute z-[200] flex h-full w-full items-center justify-center bg-black/50">
        <div class="m-2 max-w-md rounded-lg bg-white p-2 shadow-lg">
            <div class="flex items-center justify-between rounded-lg bg-pink-600 px-1 text-white">
                <div class="flex items-center space-x-1">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="h-5 w-5 text-white">
                        <path fill-rule="evenodd" d="M13.5 4.938a7 7 0 11-9.006 1.737c.202-.257.59-.218.793.039.278.352.594.672.943.954.332.269.786-.049.773-.476a5.977 5.977 0 01.572-2.759 6.026 6.026 0 012.486-2.665c.247-.14.55-.016.677.238A6.967 6.967 0 0013.5 4.938zM14 12a4 4 0 01-4 4c-1.913 0-3.52-1.398-3.91-3.182-.093-.429.44-.643.814-.413a4.043 4.043 0 001.601.564c.303.038.531-.24.51-.544a5.975 5.975 0 011.315-4.192.447.447 0 01.431-.16A4.001 4.001 0 0114 12z" clip-rule="evenodd" />
                    </svg>
                    <p class="font-['blue_highway'] text-lg">Verification</p>
                </div>
            </div>
            <div class="flex flex-col space-y-3 p-2 font-['abel'] text-gray-500">
                <p>To use Pluto app, you need to verify your Discord account. Simply log in to your Discord account and follow our verification instructions.</p>
                <div class="flex">
                    <div class="rounded-lg bg-slate-100 px-2 font-['Barlow_Condensed'] text-black/50 drop-shadow">
                        <p>Steps</p>
                    </div>
                </div>
                <div class="flex flex-col space-y-1 px-1">
                    <div class="flex items-center space-x-2">
                        <div class="flex h-5 w-5 items-center justify-center rounded-full bg-pink-600 text-xs text-white drop-shadow-md">1</div>
                        <button @onclick="LaunchAuthentication"
                            class="cursor-pointer bg-slate-100 hover:bg-slate-200 text-black/70 hover:text-black px-2 py-1 rounded-lg font-['Barlow_Condensed'] drop-shadow transition-all duration-200">Open
                            verification page</button>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="flex h-5 w-5 items-center justify-center rounded-full bg-pink-600 text-xs text-white drop-shadow-md">2</div>
                        <p>Press on the connect button</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="flex h-5 w-5 items-center justify-center rounded-full bg-pink-600 text-xs text-white drop-shadow-md">3</div>
                        <p>You will be verified automatically</p>
                    </div>
                </div>
                <div class="flex items-center justify-end pt-2">
                    <div class="rounded-lg flex items-center space-x-2 bg-slate-100 px-2 font-['Barlow_Condensed'] text-black/50 drop-shadow">
                        <img width="20" src="@(Dolo.Assets.MSPLoader)" alt="" />
                        <p>Waiting for verification</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="absolute bottom-5 left-0 flex flex-col pl-6 pt-10">
        <p class="font-[bangers] text-3xl tracking-wider text-white [text-shadow:1px_1px_2px_black]"></p>
        <p class="font-[abel] text-slate-200 opacity-60">Waiting for verification ..</p>
    </div>

</div>


@code {
    // Handler for launching the authentication process
    private async Task LaunchAuthentication()
    {
        await LicenseService.LaunchAuthenticationAsync();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (!firstRender)
            return;

        AuthenticationHub.OnTokenProcessed += (result) =>
        {
            if (!result.IsValid) return;

            NavigationManager.NavigateTo("/login");
        };
    }
}
