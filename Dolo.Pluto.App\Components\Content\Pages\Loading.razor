﻿@page "/loading"
@using Dolo.Core
@using Dolo.Pluto.App.Components.Content.Layout
@using Dolo.Pluto.App.Components.Content.Component
@using Dolo.Pluto.App.Core
@using Dolo.Pluto.App.Core.App
@using Dolo.Pluto.Shard
@using Newtonsoft.Json
@using Dolo.Pluto.Shard.Services
@using Dolo.Pluto.Shard.Services.License
@using Dolo.Pluto.Shard.Configuration
@using System.Text
@inject DialogService DialogService
@inject NavigationManager NavigationManager
@inject SwfService SwfService
@inject ApiService ApiService
@inject IAppConfiguration AppConfiguration
@inject ILicenseService LicenseService
@implements IDisposable
@* @attribute [StreamRendering(true)] *@

<LoadingTransition @ref="_loadingTransition" IsVisible="false" OnTransitionComplete="OnTransitionComplete" />

<div class="flex h-full w-full bg-[url('image.png')] bg-cover items-center justify-center transition-opacity duration-1000 @(_isTransitioning ? "opacity-0" : "opacity-100")">
    <div class="absolute left-0 bottom-5 flex flex-col pl-6 pt-10">
        <p class="font-[bangers] text-3xl tracking-wider text-white [text-shadow:1px_1px_2px_black]">@_loading</p>
        <p class="font-[abel] text-slate-200 opacity-60">@_message</p>
    </div>
</div>

@code {
    private string? _message = "Please wait ..";
    private string _loading = "Loading";
    private bool _isTransitioning;
    private LoadingTransition? _loadingTransition;
    private readonly CancellationTokenSource _cancellationTokenSource = new();

    private void SetMessage(string? message)
    {
        _message = message;
        InvokeAsync(StateHasChanged);
    }

    private Task OnTransitionComplete()
    {
        NavigationManager.NavigateTo("/login");
        return Task.CompletedTask;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (!firstRender) return;

        // Start the dot animation
        _ = DotDotAsync();

        // Add initial delay to prevent immediate navigation
        await Task.Delay(2000);

        SetMessage("Connecting to servers..");
        await Task.Delay(500);

        var toolbox = await ApiService.GetToolboxAsync();
        if (toolbox is null)
        {
            await DialogService.ShowPopupAsync(options =>
            options.SetTitle("Connection Failed")
            .SetMessage("Unable to connect to Dolo servers. This could be due to:\n\n" +
            "• Your internet connection is unstable\n" +
            "• Our servers are temporarily unavailable\n" +
            "• A firewall is blocking the connection\n\n" +
            "Please check your internet connection and try again.")
            .SetButtonText("Retry")
            .SetButtonAction(() => NavigationManager.NavigateTo("/loading", forceLoad: true))
            .AllowClose()
            );
            return;
        }

        SetMessage("Loading Pluto tool..");
        await Task.Delay(300);

        var tool = toolbox.GetTool("Pluto");
        if (tool is null)
        {
            await DialogService.ShowPopupAsync(options =>
            options.SetTitle("Tool Loading Failed")
            .SetMessage("The Pluto tool could not be loaded from our servers. This might happen because:\n\n" +
            "• The tool is temporarily unavailable\n" +
            "• There's a server-side configuration issue\n" +
            "• Your client version may be outdated\n\n" +
            "Please try restarting the application or contact support if the problem persists.")
            .SetButtonText("Retry")
            .SetButtonAction(() => NavigationManager.NavigateTo("/loading", forceLoad: true))
            .AllowClose()
            );
            return;
        }        if (tool.Windows.Maintenance.Enabled)
        {
            NavigationManager.NavigateTo("/maintenance");
            return;
        }        if (new Version(AppConfiguration.AppVersion) < new Version(tool.Windows.Version!))
        {
            NavigationManager.NavigateTo("/update");
            return;
        }

        SetMessage("Verifying license..");
        await Task.Delay(300);

        var license = await LicenseService.IsAllowedToUseToolAsync();
        if (!license.IsValid)
        {
            NavigationManager.NavigateTo("/license");
            return;
        }

        SetMessage("Preparing login..");
        await Task.Delay(500);

        // Start transition
        _isTransitioning = true;
        StateHasChanged();
        
        await Task.Delay(1000);

        // Show loading transition for 2 seconds before navigating
        if (_loadingTransition != null)
            await _loadingTransition.ShowTransitionAsync(2000);
    }

    private async Task DotDotAsync()
    {
        while (!_cancellationTokenSource.IsCancellationRequested)
        {
            for (var i = 0; i < 3; i++)
            {
                _loading += ".";
                await InvokeAsync(StateHasChanged);
                await Task.Delay(500);
            }
            
            _loading = "Loading";
            await InvokeAsync(StateHasChanged);
            await Task.Delay(300);
        }
    }

    public void Dispose()
    {
        _cancellationTokenSource.Cancel();
        _cancellationTokenSource.Dispose();
    }
}