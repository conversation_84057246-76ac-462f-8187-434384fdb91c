﻿using Dolo.Core.Extension;
using Dolo.Planet;
using Dolo.Planet.Entities;
using Dolo.Planet.Enums;
using Dolo.Pluto.App.Components.Content.Layout;
using Dolo.Pluto.App.Core.App;
using Dolo.Pluto.Components.UI.Toast;
using Dolo.Pluto.Shard.Services;
using Dolo.Pluto.Shard.User;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;

namespace Dolo.Pluto.App.Components.Content.Pages;

public class LoginService : IService
{
    private readonly DialogService _dialogService;
    private readonly NavigationManager _navigationManager;
    private readonly SwfService _swfService;

    public LoginService(DialogService dialogService, NavigationManager navigationManager, SwfService swfService)
    {
        _navigationManager = navigationManager;
        _dialogService = dialogService;
        _swfService = swfService;
    }

    /// <summary>
    ///     MspClient instance
    /// </summary>
    public MspClient? MspClient { get; set; }

    /// <summary>
    ///     MspUser
    /// </summary>
    public MspLogin MspUser => IsUsable ? MspClient!.User : new MspLogin();

    /// <summary>
    ///     List of all saved accounts
    /// </summary>
    public List<Account> Accounts => AppAccount.GetAccounts().Reversing();

    /// <summary>
    ///     Checks if the user is logged in and not null
    /// </summary>
    public bool IsUsable => MspClient is { User.LoggedIn: true };

    /// <summary>
    ///     Checks whenever the user has multiple accounts
    /// </summary>
    public bool HasMultipleAccounts => Accounts.Count > 0;

    /// <summary>
    ///     Logout to the current account
    /// </summary>
    public async Task LogOutAsync(bool isCustomNavigation = false)
    {
        FarmService.Dispose();
        FriendService.Dispose();
        ShopAnimationService.Dispose();
        ShopBeautyService.Dispose();
        ToolService.Dispose();

        if (MspClient != null)
        {
            await MspClient.LogOutAsync();
            await _swfService.TryStopSwfAsync("msp-world");
        }

        if (!isCustomNavigation) _navigationManager.NavigateTo("/login");
    }

    /// <summary>
    ///     Login to the msp account and navigate to the home page
    /// </summary>
    public async Task<bool> LoginAsync(string? username, string? password, Server? server)
    {
        if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
        {
            await _dialogService.ShowToastAsync("Please enter your username and password", ToastType.Error);
            return false;
        }

        if (server is null)
        {
            await _dialogService.ShowToastAsync("Please select a server", ToastType.Error);
            return false;
        }

        await _dialogService.ShowLoaderAsync();
        MspClient = new MspClient(a =>
        {
            a.Username = username;
            a.Password = password;
            a.Server = server;
            a.UseLogger(a => a.AddConsole().SetMinimumLevel(LogLevel.Debug));
            a.UseOriginalBehaviour();
        });

        var login = await MspClient.LoginAsync();
        if (!login.LoggedIn)
        {
            if (login.StatusCode == LoginStatusCode.InvalidCredentials) AppAccount.Remove(new Account(login));

            await _dialogService.HideLoaderAsync();
            await _dialogService.ShowPopupAsync(a =>
                a.SetTitle("Failed to Login").SetMessage(login.Status).AllowClose());
            return false;
        }

        AppAccount.Append(new Account(login)
        {
            LastUsedAt = DateTime.Now
        });
        await _dialogService.HideLoaderAsync();
        return true;
    }
}