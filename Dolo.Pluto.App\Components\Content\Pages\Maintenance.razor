@page "/maintenance"
@using Dolo.Core
@using Dolo.Pluto.App.Components.Content.Layout
@using Dolo.Pluto.App.Core
@using Dolo.Pluto.App.Core.App
@using Dolo.Pluto.Shard.Services
@using Dolo.Pluto.Shard.Configuration
@using Dolo.Pluto.Shard
@inject NavigationManager NavigationManager
@inject ApiService ApiService
@inject IAppConfiguration AppConfiguration

<div class="flex h-full w-full bg-[url('image.png')] bg-cover items-center justify-center">
    <div class="absolute z-[200] flex h-full w-full items-center justify-center bg-black/50">
        <div class="m-2 max-w-md rounded-lg bg-white p-2 shadow-lg">
            <div class="flex items-center justify-between rounded-lg bg-orange-600 px-1 text-white">
                <div class="flex items-center space-x-1">
                    <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    <p class="font-['blue_highway'] text-lg">Maintenance</p>
                </div>
            </div>            <div class="flex flex-col space-y-3 p-2 font-['abel'] text-gray-500">
                <div class="rounded-lg bg-orange-50 border border-orange-200 p-3">
                    <p class="font-['abel'] text-gray-700 text-center">@_maintenanceMessage</p>
                </div>
                <div class="flex">
                    <div class="rounded-lg bg-slate-100 px-2 font-['Barlow_Condensed'] text-black/50 drop-shadow">
                        <p>Status</p>
                    </div>
                </div>
                <div class="flex flex-col space-y-1 px-1">
                    <div class="flex items-center justify-between">
                        <span class="font-['abel'] text-gray-500">Current Status:</span>
                        <span class="font-['abel'] font-semibold text-orange-500">Under Maintenance</span>
                    </div>
                    @if (_lastCheckTime.HasValue)
                    {
                        <div class="flex items-center justify-between">
                            <span class="font-['abel'] text-gray-500">Last Check:</span>
                            <span class="font-['abel'] font-semibold text-blue-500">@_lastCheckTime.Value.ToString("HH:mm:ss")</span>
                        </div>
                    }
                </div>
                <div class="flex">
                    <div class="rounded-lg bg-slate-100 px-2 font-['Barlow_Condensed'] text-black/50 drop-shadow">
                        <p>Instructions</p>
                    </div>
                </div>
                <div class="flex flex-col space-y-1 px-1">
                    <div class="flex items-center space-x-2">
                        <div class="flex h-5 w-5 items-center justify-center rounded-full bg-orange-600 text-xs text-white drop-shadow-md">1</div>
                        <p>Click refresh to check maintenance status</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="flex h-5 w-5 items-center justify-center rounded-full bg-orange-600 text-xs text-white drop-shadow-md">2</div>
                        <p>Wait for maintenance to complete</p>
                    </div>
                </div>
                <div class="flex justify-center pt-2">
                    <Button IsFullWidth="true" Name="@(_isRefreshing ? "Refreshing .." : "Refresh")" OnClick="RefreshAsync"/>
                </div>
                <div class="flex items-center justify-end pt-2">
                    <div class="rounded-lg flex items-center space-x-2 bg-slate-100 px-2 font-['Barlow_Condensed'] text-black/50 drop-shadow">
                        <img width="20" src="@(Dolo.Assets.MSPLoader)" alt="" />
                        <p>Maintenance in progress</p>
                    </div>
                </div>
            </div>
        </div>
    </div>   
     <div class="absolute bottom-5 left-0 flex flex-col pl-6 pt-10">
        <p class="font-[bangers] text-3xl tracking-wider text-white [text-shadow:1px_1px_2px_black]">Maintenance</p>
        <p class="font-[abel] text-slate-200 opacity-60">Please wait for maintenance to complete..</p>
    </div>
</div>

@code {
    private string _maintenanceMessage = "Please wait while we improve your experience.";
    private bool _isRefreshing;
    private DateTime? _lastCheckTime;

    protected override async Task OnInitializedAsync()
    {
        await LoadMaintenanceMessageAsync();
    }

    private async Task LoadMaintenanceMessageAsync()
    {
        try
        {
            var toolbox = await ApiService.GetToolboxAsync();
            var tool = toolbox?.GetTool("Pluto");
            
            if (tool?.Windows.Maintenance.Enabled == true && !string.IsNullOrEmpty(tool.Windows.Maintenance.Message))
            {
                _maintenanceMessage = tool.Windows.Maintenance.Message;
            }
        }
        catch
        {
            // Use default message if API call fails
        }
    }

    private async Task RefreshAsync()
    {
        if (_isRefreshing) return;

        _isRefreshing = true;
        _lastCheckTime = DateTime.Now;
        StateHasChanged();

        try
        {
            await Task.Delay(1000); // Brief delay for better UX
            
            var toolbox = await ApiService.GetToolboxAsync();
            if (toolbox is null)
            {
                return;
            }

            var tool = toolbox.GetTool("Pluto");
            if (tool is null)
            {
                return;
            }

            // Check if maintenance is still enabled
            if (!tool.Windows.Maintenance.Enabled)
            {
                // Maintenance is over, navigate back to loading
                NavigationManager.NavigateTo("/loading");
                return;
            }

            // Update maintenance message if it changed
            if (!string.IsNullOrEmpty(tool.Windows.Maintenance.Message))
            {
                _maintenanceMessage = tool.Windows.Maintenance.Message;
            }
        }
        catch
        {
            // Handle refresh errors silently
        }
        finally
        {
            _isRefreshing = false;
            StateHasChanged();
        }
    }
}
