@page "/update"
@using Dolo.Core
@using Dolo.Pluto.App.Components.Content.Layout
@using Dolo.Pluto.App.Core
@using Dolo.Pluto.App.Core.App
@using Dolo.Pluto.Shard.Services
@using Dolo.Pluto.Shard.Configuration
@using Dolo.Pluto.Shard
@inject NavigationManager NavigationManager
@inject ApiService ApiService
@inject IAppConfiguration AppConfiguration

<div class="flex h-full w-full bg-[url('image.png')] bg-cover items-center justify-center">
    <div class="absolute z-[200] flex h-full w-full items-center justify-center bg-black/50">
        <div class="m-2 max-w-md rounded-lg bg-white p-2 shadow-lg">
            <div class="flex items-center justify-between rounded-lg bg-green-600 px-1 text-white">
                <div class="flex items-center space-x-1">
                    <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                    </svg>
                    <p class="font-['blue_highway'] text-lg">Update Available</p>
                </div>
            </div>
            <div class="flex flex-col space-y-3 p-2 font-['abel'] text-gray-500">
                <p>A new version of Pluto is available. Please update the tool in the toolbox to continue using the latest features.</p>
                <div class="flex">
                    <div class="rounded-lg bg-slate-100 px-2 font-['Barlow_Condensed'] text-black/50 drop-shadow">
                        <p>Version Information</p>
                    </div>
                </div>
                <div class="flex flex-col space-y-1 px-1">
                    <div class="flex items-center justify-between">
                        <span class="font-['abel'] text-gray-500">Current Version:</span>
                        <span class="font-['abel'] font-semibold text-red-500">v@(AppConfiguration.AppVersion)</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="font-['abel'] text-gray-500">Latest Version:</span>
                        <span class="font-['abel'] font-semibold text-green-500">v@(_latestVersion)</span>
                    </div>
                </div>                <div class="flex">
                    <div class="rounded-lg bg-slate-100 px-2 font-['Barlow_Condensed'] text-black/50 drop-shadow">
                        <p>Steps</p>
                    </div>
                </div>
                <div class="flex flex-col space-y-1 px-1">
                    <div class="flex items-center space-x-2">
                        <div class="flex h-5 w-5 items-center justify-center rounded-full bg-green-600 text-xs text-white drop-shadow-md">1</div>
                        <p>Open the Pluto Toolbox</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="flex h-5 w-5 items-center justify-center rounded-full bg-green-600 text-xs text-white drop-shadow-md">2</div>
                        <p>Close this application</p>
                    </div>
                          <div class="flex items-center space-x-2">
                        <div class="flex h-5 w-5 items-center justify-center rounded-full bg-green-600 text-xs text-white drop-shadow-md">3</div>
                        <p>Update Pluto</p>
                    </div>
                </div>
                <div class="flex justify-center pt-2">
                    <Button IsFullWidth="true" Name="Close Application" OnClick="CloseApplication"/>

                </div>
                <div class="flex items-center justify-end pt-2">
                    <div class="rounded-lg flex items-center space-x-2 bg-slate-100 px-2 font-['Barlow_Condensed'] text-black/50 drop-shadow">
                        <img width="20" src="@(Dolo.Assets.MSPLoader)" alt="" />
                        <p>Update required</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private string _latestVersion = "Unknown";

    protected override async Task OnInitializedAsync()
    {
        await LoadVersionInfoAsync();
    }

    private async Task LoadVersionInfoAsync()
    {
        try
        {
            var toolbox = await ApiService.GetToolboxAsync();
            var tool = toolbox?.GetTool("Pluto");
            
            if (!string.IsNullOrEmpty(tool?.Windows.Version))
            {
                _latestVersion = tool.Windows.Version;
            }
        }
        catch
        {
            // Use default if API call fails
        }
    }

    private void CloseApplication()
    {
        Environment.Exit(0);
    }
}
