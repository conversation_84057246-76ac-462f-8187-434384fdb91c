using System;
using Dolo.Pluto.Shard;
using Dolo.Pluto.Shard.License;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App.Components.Content.Services;

public class PermissionService(ApiService apiService) : IService
{
    public async Task<bool> HasPermissionsAsync(LicensePermission permission)
    {
        var permissions = await apiService.GetLicensePermissionsAsync();
        return permissions?.Contains(permission) ?? false;
    }
}
