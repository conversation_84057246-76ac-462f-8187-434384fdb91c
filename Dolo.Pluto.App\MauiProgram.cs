﻿using System.Runtime.InteropServices;
using Dolo.Pluto.Shard.Extensions;
using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.App;

public static class MauiProgram
{
    [DllImport("kernel32.dll")]
    private static extern bool AllocConsole();

    public static MauiApp CreateMauiApp()
    {
#if !RELEASE
        AllocConsole();
#endif

        var config = new PlutoConfiguration();
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp<App>()
            .ConfigureSharedWebHost(config)
            .ConfigureFonts(fonts => { fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular"); });

        var serviceTypes = AppDomain.CurrentDomain.GetAssemblies()
            .SelectMany(s => s.GetTypes())
            .Where(p => typeof(IService).IsAssignableFrom(p) && p.<PERSON>lass);

        foreach (var serviceType in serviceTypes)
            builder.Services.AddScoped(serviceType);

        builder.Services.AddMauiBlazorWebView();
        builder.Services.AddSharedServices(config);

#if DEBUG
        builder.Services.AddBlazorWebViewDeveloperTools();
#endif

        return builder.Build();
    }
}