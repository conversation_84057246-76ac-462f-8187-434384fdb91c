﻿using MongoDB.Bson.Serialization.Attributes;

namespace Dolo.Pluto.Shard.Bot.Apple;

[BsonIgnoreExtraElements]
public class VoiceUser
{

    public VoiceUser()
    {
        Moderator = new();
        Banned = new();
        Muted = new();
    }
    public ulong Owner { get; set; }
    public ulong Channel { get; set; }
    public ulong TextChannel { get; set; }
    public List<ulong> Moderator { get; set; }
    public List<ulong> Banned { get; set; }
    public List<ulong> Muted { get; set; }
}