@using Dolo.Pluto.Shard.Configuration
@inherits ComponentBase
@inject IAppConfiguration AppConfiguration

<!-- Automatically load shared resources from Shard -->
<SharedResourceAutoLoader/>

<div class="@(IsHidden ? "hidden" : "") fixed inset-0 z-50 flex items-center justify-center bg-bg-base bg-white">
    <div class="w-full max-w-md p-5">
        <!-- Logo and App Name -->
        <div class="flex items-center justify-center mb-6">
            <div
                class="w-8 h-8 rounded-lg bg-bg-surface flex items-center justify-center mr-2 border border-border-l1 shadow-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-text-main" viewBox="0 0 20 20"
                     fill="currentColor">
                    <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"/>
                </svg>
            </div>
            <h1 class="text-lg font-bold font-jakarta text-span-default">Pluto <span
                    class="text-span-muted">@ToolName</span></h1>
        </div>

        <!-- Main content box -->
        <div class="bg-bg-surface rounded-lg border border-border-l1 p-5 shadow-sm">
            <!-- Header with status -->
            <div class="mb-4 flex flex-row items-center justify-between">
                <div class="flex flex-col">
                    <h3 class="font-jakarta text-sm font-bold tracking-wide text-span-default">@StatusTitle</h3>
                    <div class="flex items-center mt-1">
                        <div class="h-1.5 w-1.5 rounded-full bg-text-main animate-pulse"></div>
                        <span
                            class="ml-1.5 text-[10px] font-medium tracking-wide text-span-muted">@StatusBadgeText</span>
                    </div>
                </div>

                <div class="rounded-lg border border-border-l1 bg-bg-surface-hover px-2.5 py-1.5 shadow-sm">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                             class="h-3 w-3 text-span-muted mr-1">
                            <path fill-rule="evenodd"
                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                                  clip-rule="evenodd"/>
                        </svg>
                        <span class="text-xs font-medium text-span-default">@StatusTitle</span>
                    </div>
                </div>
            </div>

            <!-- Progress section -->
            <div class="mb-4">
                <div class="mb-2 flex items-center justify-between">
                    <span class="text-xs font-medium text-span-muted">Current Operation</span>
                    <div class="rounded-full bg-bg-surface-hover px-2 py-0.5 text-[10px] font-medium text-span-default">
                        @OperationDetail
                    </div>
                </div>

                <!-- Progress bar -->
                <div class="h-2 w-full overflow-hidden rounded-full bg-bg-surface border border-border-l1 shadow-inner">
                    <div class="h-full bg-text-main transition-all duration-300 rounded-full"
                         style="width: @(ProgressPercentage)%"></div>
                </div>
            </div>

            <!-- Message area -->
            <div class="text-xs text-span-muted mb-4">
                @MessageArea
            </div>

            <!-- Status info box -->
            <div class="rounded-lg border border-border-l1 bg-bg-surface-hover p-3 mb-4 shadow-sm">
                <!-- Initial state for normal initialization -->
                <div class="@(ShowInitializationContent ? "" : "hidden")">
                    <div class="flex items-center text-xs font-medium text-span-default mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5 text-span-muted"
                             viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd"
                                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                                  clip-rule="evenodd"/>
                        </svg>
                        <span>Initialization Status</span>
                    </div>
                    <p class="text-xs text-span-subtle">We're getting everything ready for you. This should only take a
                        moment.</p>
                </div>

                <!-- Content for maintenance state -->
                <div class="@(ShowMaintenanceContent ? "" : "hidden")">
                    <div class="flex items-center text-xs font-medium text-span-default mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5 text-warning"
                             viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd"
                                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                  clip-rule="evenodd"/>
                        </svg>
                        <span>Maintenance Required</span>
                    </div>
                    <p class="text-xs text-span-subtle">Our servers are currently undergoing maintenance. Please try
                        again in a few minutes.</p>
                </div>

                <!-- Content for update state -->
                <div class="@(ShowUpdateContent ? "" : "hidden")">
                    <div class="flex items-center text-xs font-medium text-span-default mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5 text-warning"
                             viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd"
                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                                  clip-rule="evenodd"/>
                        </svg>
                        <span>Update Required</span>
                    </div>
                    <p class="text-xs text-span-subtle">Your current version is outdated. Please update to the latest
                        version to continue.</p>
                </div>

                <!-- Content for license state -->
                <div class="@(ShowLicenseContent ? "" : "hidden")">
                    <div class="flex items-center text-xs font-medium text-span-default mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5 text-text-main"
                             viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd"
                                  d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                                  clip-rule="evenodd"/>
                        </svg>
                        License Verification
                    </div>
                    <p class="text-xs text-span-subtle mb-3">Verification is needed to continue.</p>

                    <!-- Verify button and loading spinner -->
                    <div class="flex items-center justify-center mb-3">
                        @if (IsVerifying)
                        {
                            <div class="flex items-center justify-center space-x-2">
                                <div
                                    class="h-4 w-4 animate-spin rounded-full border-2 border-solid border-text-main border-t-transparent"></div>
                                <span class="text-xs text-span-muted">Verifying your license...</span>
                            </div>
                        }
                        else
                        {
                            <button @onclick="StartVerification"
                                    class="w-full bg-bg-surface hover:bg-bg-surface-hover text-span-default px-4 py-1.5 rounded-lg transition-all text-xs border border-border-l1 hover:border-border-l2 shadow-sm hover:shadow flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5 text-text-main"
                                     viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd"
                                          d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                          clip-rule="evenodd"/>
                                </svg>
                                Verify License
                            </button>
                        }
                    </div>
                </div>
            </div>

            <!-- Action buttons with consistent styling -->
            <div class="flex justify-center gap-2">
                <button @onclick="RetryInitialization"
                        class="@(ShowRetryButton ? "" : "hidden") bg-bg-surface-hover hover:brightness-110 text-span-default px-4 py-1.5 rounded-lg transition-all text-xs border border-border-l1 hover:border-border-l2 shadow-sm hover:shadow flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5" viewBox="0 0 20 20"
                         fill="currentColor">
                        <path fill-rule="evenodd"
                              d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                              clip-rule="evenodd"/>
                    </svg>
                    Retry
                </button>
                <button @onclick="CloseApplication"
                        class="@(ShowCloseButton ? "" : "hidden") bg-bg-surface hover:bg-bg-surface-hover text-span-muted hover:text-span-hover px-4 py-1.5 rounded-lg transition-all text-xs border border-border-l1 hover:border-border-l2 shadow-sm">
                    Close
                </button>
            </div>
        </div>

        <!-- Footer section -->
        <div class="text-center mt-4 text-[10px] text-span-subtle">
            <p>Pluto @ToolName v@(AppConfiguration.AppVersion) — <span
                    class="text-span-muted hover:text-span-hover transition-colors cursor-pointer">discord.gg/dolo</span>
            </p>
        </div>
    </div>
</div>
