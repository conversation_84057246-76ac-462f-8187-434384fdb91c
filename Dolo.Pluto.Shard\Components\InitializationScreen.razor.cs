using Dolo.Pluto.Shard.Services.Initialization;
using Microsoft.AspNetCore.Components;
using Dispatcher = Microsoft.AspNetCore.Components.Dispatcher;

namespace Dolo.Pluto.Shard.Components;

public partial class InitializationScreen : ComponentBase
{
    [Inject] private IInitializationStateManager StateManager { get; set; } = null!;
    [Inject] private IInitializationProgressTracker ProgressTracker { get; set; } = null!;
    [Inject] private IInitializationWorkflow Workflow { get; set; } = null!;

    [Parameter] public string ToolName { get; set; } = "Tool";

    public bool IsHidden => StateManager.IsHidden;
    public string StatusTitle => StateManager.StatusTitle;
    public string StatusBadgeText => StateManager.StatusBadgeText;
    public string OperationDetail => StateManager.OperationDetail;
    public string MessageArea => StateManager.MessageArea;
    public int ProgressPercentage => ProgressTracker.ProgressPercentage;
    public bool IsVerifying => StateManager.IsVerifying;
    public bool ShowInitializationContent => StateManager.ShowInitializationContent;
    public bool ShowMaintenanceContent => StateManager.ShowMaintenanceContent;
    public bool ShowUpdateContent => StateManager.ShowUpdateContent;
    public bool ShowLicenseContent => StateManager.ShowLicenseContent;
    public bool ShowRetryButton => StateManager.ShowRetryButton;
    public bool ShowCloseButton => StateManager.ShowCloseButton;

    public event Action? OnInitializationCompleted;

    protected override async Task OnInitializedAsync()
    {
        StateManager.OnStateChanged += () => InvokeAsync(StateHasChanged);
        ProgressTracker.OnProgressUpdated += (detail, percentage) => InvokeAsync(() =>
        {
            StateManager.OperationDetail = detail;
            StateHasChanged();
        });
        Workflow.OnInitializationCompleted += () => InvokeAsync(() => OnInitializationCompleted?.Invoke());

        await InitializeAsync();
    }

    public void Hide()
    {
        StateManager.Hide();
        OnInitializationCompleted?.Invoke();
    }

    public void Show()
    {
        StateManager.Show();
    }

    private async Task InitializeAsync()
    {
        await Workflow.ExecuteInitializationAsync(ToolName);
    }

    public void ShowInitializing()
    {
        StateManager.ShowInitializing(ToolName);
    }

    public void ShowMaintenance()
    {
        StateManager.ShowMaintenanceState("The tool is currently under maintenance. Please check back later.");
    }

    public void ShowUpdate()
    {
        StateManager.ShowUpdateState("Your current version is outdated. Please update to the latest version.");
    }

    public void ShowLicense(string licenseMessage = "Please verify your license to continue using this tool.")
    {
        StateManager.ShowLicenseState(licenseMessage);
    }

    public void CompleteInitialization()
    {
        ProgressTracker.CompleteProgress();
        Hide();
    }

    public void RetryInitialization()
    {
        _ = InitializeAsync();
    }

    public static void CloseApplication()
    {
        Application.Current?.Quit();
    }

    public async Task StartVerification()
    {
        await Workflow.StartVerificationAsync();
    }
}