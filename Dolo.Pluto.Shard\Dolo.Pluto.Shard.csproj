<Project Sdk="Microsoft.NET.Sdk.Razor">

    <PropertyGroup>
        <TargetFramework>net10.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <LangVersion>preview</LangVersion>
        <EnableDefaultCssItems>false</EnableDefaultCssItems>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Dolo.MovieStarPlanet.Components\Dolo.MovieStarPlanet.Components.csproj"/>
        <ProjectReference Include="..\Dolo.Planet\Dolo.Planet.csproj"/>
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="JsonSubTypes" Version="2.0.1"/>
        <PackageReference Include="Microsoft.AspNetCore.App" Version="2.2.8"/>
        <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="10.0.0-preview.6.25358.103"/>
        <PackageReference Include="Microsoft.AspNetCore.SignalR.Client.Core" Version="10.0.0-preview.6.25358.103"/>
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="10.0.0-preview.6.25358.103"/>
        <PackageReference Include="Microsoft.Maui.Controls" Version="10.0.0-preview.6.25359.8"/>
        <PackageReference Include="Serilog.Extensions.Logging.File" Version="9.0.0-dev-02302"/>
    </ItemGroup>

    <ItemGroup>
        <!-- Embed all wwwroot files as resources for shared access -->
        <EmbeddedResource Include="wwwroot\**\*"/>
    </ItemGroup>
</Project>
