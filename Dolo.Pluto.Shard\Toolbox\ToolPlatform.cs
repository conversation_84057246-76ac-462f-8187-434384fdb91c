using System.Reflection;
using MongoDB.Bson.Serialization.Attributes;

namespace Dolo.Pluto.Shard.Toolbox;

[Obfuscation(Feature = "apply to member * when method: renaming", Exclude = true)]
[Obfuscation(Feature = "internalization", Exclude = true)]
[BsonIgnoreExtraElements]

public class ToolPlatform
{
    public string? Version { get; set; }
    public string? DownloadUrl { get; set; }
    public ToolMaintenance Maintenance { get; set; } = new();
    public List<ToolChangelog> Changelog { get; set; } = [];
    public bool IsUpdateAvailable(string currentVersion) => Version != currentVersion;
}