using System;
using System.Reflection;
using Dolo.Pluto.Shard;
using Dolo.Pluto.Shard.Configuration;

[assembly: Obfuscation(Feature = "merge with [satellites internalization=none] Dolo.*.dll", Exclude = false)]
[assembly: Obfuscation(Feature = "merge with [satellites internalization=none] Dolo.Planet.dll", Exclude = false)]
[assembly: Obfuscation(Feature = "merge with [satellites internalization=none] Dolo.Pluto.Shard.dll", Exclude = false)]
[assembly: Obfuscation(Feature = "string encryption", Exclude = false)]
[assembly: Obfuscation(Feature = "encrypt resources [compress]", Exclude = false)]
[assembly: Obfuscation(Feature = "rename symbol names with printable characters", Exclude = false)]
[assembly: Obfuscation(Feature = "code control flow obfuscation", Exclude = false)]
[assembly: Obfuscation(Feature = "encrypt symbol names with password 'XLWKED)$!!%(&$)&)))-$(FF$%)-)$&/)-�=?�&?�&)IIIU'", Exclude = false)]
[assembly: Obfuscation(Feature = "encrypt serializable symbol names with password 'XLWKED)$!!%(&$)&)))-$(FF$%)-)$&/)-�=?�&?�&)IIIU'", Exclude = false)]
[assembly: Obfuscation(Feature = "apply to type * when internal and class: renaming", Exclude = false, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "sanitize resources", Exclude = false)]
[assembly: Obfuscation(Feature = "apply to type * when class: internalization", Exclude = false)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Nebula.Entities.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Nebula.Entities.Internal.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Planet.Entities.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Planet.NET.Entities.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.*: all", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.*: internalization", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.*: virtualization", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.*: all", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.*: internalization", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.*: virtualization", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type *AMFTicket*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type *AMFTicket*: all", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type *AMFBuilder*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type *AMFBuilder*: all", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type *AMFSerializer*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type *AMFSerializer*: all", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.AMF3.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.Attribute.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.Collections.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.Collections.Generic.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.Configuration.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.Context.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.DependencyInjection.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.Exceptions.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.Invocation.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.Messaging.Api.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.Messaging.Api.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.Messaging.Api.Event.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.Messaging.Api.Messaging.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.Messaging.Api.Peristence.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.Messaging.Api.Service.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Core.AMF3.Fluorine.Messaging.Config.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Pluto.Shard.*: renaming", Exclude = true, ApplyToMembers = true)]
[assembly: Obfuscation(Feature = "apply to type Dolo.Pluto.Shard.*: apply to member * when method or constructor: virtualization", Exclude = false)]
namespace Dolo.Pluto.Tool.Autograph;


public class AutographConfiguration : AppConfiguration {
    public override string AppId => "autograph";

    public override string AppVersion => "2025.7.1";

    public override string AppHub => "ag-hub";
}
