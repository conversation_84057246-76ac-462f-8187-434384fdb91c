@using System
@inherits ComponentBase
@page "/"
@using Dolo.Pluto.Tool.Autograph.Services
@using Dolo.Pluto.Tool.Autograph.Components.Shared
@using Dolo.Pluto.Shard.Components.Toast

<InitializationScreen @ref="InitializationScreen" ToolName="Autograph"/>
<AddAccountDialog @ref="AddAccountDialog"/>
<ToastContainer/>

<div class="h-full">
    <div class="flex flex-col h-screen"> <!-- Combined Top Bar -->
        <TopBar OnToggleDeveloperMode="ToggleDeveloperMode" OnStopAllAutographs="StopAllAutographs"/>
        <div class="flex flex-1 overflow-hidden"> <!-- Left Sidebar -->
            <Sidebar Accounts="AccountServiceInstance.Accounts" SelectedAccount="ViewModel.SelectedAccount"
                     OnAccountSelected="SelectAccount" OnAddAccountClicked="() => AddAccountDialog?.Show()"
                     IsAutoGraphRunningForSelectedAccount="AutoGraphServiceInstance.IsAccountRunning(ViewModel.SelectedAccount)"
                     IsAccountRunning="(account) => AutoGraphServiceInstance.IsAccountRunning(account)"
                     OnResetTimer="ResetAccountTimer"/>

            <!-- Right Content Area -->
            <div class="flex-1 flex flex-col bg-bg-base overflow-hidden">
                <div class="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-white/5">
                    @if (!ViewModel.HasAccounts || ViewModel.SelectedAccount == null)
                    {
                        <!-- No Account UI for Right Content Area -->
                        <div class="h-full flex flex-col items-center justify-center p-6">
                            <div
                                class="w-12 h-12 rounded-full bg-bg-surface flex items-center justify-center mb-3 border border-border-l1 shadow-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-span-muted"
                                     viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                          clip-rule="evenodd"/>
                                </svg>
                            </div>

                            <h2 class="text-base font-bold text-span-default mb-2 font-jakarta">No Account Selected</h2>
                            <p class="text-xs text-span-muted text-center max-w-xs mb-4">
                                @if (!ViewModel.HasAccounts)
                                {
                                    <span>You haven't added any accounts yet. Add an account from the sidebar to get started.</span>
                                }
                                else
                                {
                                    <span>Please select an account from the sidebar to manage it.</span>
                                }
                            </p>
                        </div>
                    }
                    else
                    {
                        <div class="p-6">
                            <!-- Selected user box -->
                            <div
                                class="bg-bg-surface border border-border-l1 rounded-xl shadow-sm overflow-hidden mb-6">
                                <!-- Horizontal user content -->
                                <div class="p-3 flex items-center justify-between">
                                    <!-- Left side with avatar and user info -->
                                    <div class="flex items-center">
                                        <!-- User avatar -->
                                        <div
                                            class="relative w-12 h-12 bg-bg-surface-hover rounded-lg overflow-hidden p-0.5 transition-transform duration-200 hover:scale-105 ring-1 ring-ring-focus-accent/20 mr-3">
                                            <div
                                                class="w-full h-full rounded-md overflow-hidden bg-bg-avatar flex items-center justify-center">
                                                <img src="@(ViewModel.SelectedAccount?.AvatarUrl)"
                                                     alt="@(ViewModel.SelectedAccount?.Username)"
                                                     class="w-full h-full object-cover"
                                                     onerror="this.onerror=null; this.src='https://cdn.discordapp.com/attachments/944722654907219988/1062455928772898826/1.png';">
                                            </div>
                                            <span
                                                class="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 rounded-full border-2 border-bg-surface bg-text-main"></span>
                                        </div>

                                        <!-- User details -->
                                        <div> <!-- Username with VIP status -->
                                            <h2 class="flex items-center text-base font-bold font-jakarta @(ViewModel.SelectedAccount?.MspActor?.IsVip == true ? "text-amber-400" : "text-span-default")">
                                                @(ViewModel.SelectedAccount?.Username)
                                                @if (ViewModel.SelectedAccount?.MspActor?.IsVip == true)
                                                {
                                                    <img
                                                        src="https://raw.githubusercontent.com/cydolo/assets/refs/heads/main/moviestarplanet/vip.png"
                                                        alt="VIP" class="ml-1.5 h-4 w-7"/>
                                                }
                                            </h2>

                                            <!-- Server info -->
                                            <div class="flex items-center">
                                                <span
                                                    class="text-xs text-span-muted">Level @(ViewModel.SelectedAccount?.MspActor?.Level)</span>
                                                <span class="mx-1.5 text-xs text-span-muted">•</span>
                                                <span class="inline-flex items-center text-xs">
                                                    <img src="@(ViewModel.SelectedAccount?.ServerFlagUrl)"
                                                         alt="@(ViewModel.SelectedAccount?.ServerDisplayName)"
                                                         class="w-4 h-4 mr-1 rounded-sm"/>
                                                    <span
                                                        class="text-span-muted">@(ViewModel.SelectedAccount?.Server.ToString())</span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Delete button -->
                                    <button @onclick="() => RemoveAccount(ViewModel.SelectedAccount!)"
                                            class="bg-bg-surface-hover hover:brightness-110 text-span-error px-3 py-1.5 rounded-lg transition-all duration-200 flex items-center text-xs gap-1.5 border border-border-l1 hover:border-border-l2 shadow-sm hover:shadow">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" viewBox="0 0 20 20"
                                             fill="currentColor">
                                            <path fill-rule="evenodd"
                                                  d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                        Delete
                                    </button>
                                </div>

                                <!-- Target user input and Start Autograph button -->
                                <div class="p-3 pt-0 flex items-center gap-3">
                                    <div class="flex-1">
                                        <div class="relative">
                                            <div
                                                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-span-muted"
                                                     viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd"
                                                          d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                                          clip-rule="evenodd"/>
                                                </svg>
                                            </div>
                                            <input type="text" @bind="@ViewModel.SelectedAccount!.TargetUsername"
                                                   @bind:event="oninput"
                                                   class="w-full bg-bg-surface border border-border-l1 rounded-lg pl-10 pr-3 py-2 text-sm focus:outline-none focus:border-border-l2 transition-common duration-200"
                                                   placeholder="Enter target username"/>
                                        </div>
                                    </div>
                                    <button @onclick="() => StartAutographAsync(ViewModel.SelectedAccount!)"
                                            disabled="@(!CanStartAutograph(ViewModel.SelectedAccount!))"
                                            class="@(!CanStartAutograph(ViewModel.SelectedAccount!) ? "bg-bg-surface-hover cursor-not-allowed" : "bg-bg-surface-hover hover:brightness-110 cursor-pointer") text-span-default px-4 py-2 rounded-lg transition-all duration-200 flex items-center text-xs gap-1.5 font-medium border border-border-l1 hover:border-border-l2 shadow-sm hover:shadow">
                                        @if (AutoGraphServiceInstance.IsAccountRunning(ViewModel.SelectedAccount!))
                                        {
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5"
                                                 viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd"
                                                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z"
                                                      clip-rule="evenodd"/>
                                            </svg>
                                            <span>Stop</span>
                                        }
                                        else
                                        {
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5"
                                                 viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd"
                                                      d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                                                      clip-rule="evenodd"/>
                                            </svg>
                                            <span>Start</span>
                                        }
                                    </button>
                                </div>

                                <!-- Error message display -->
                                @{
                                    var errMsg = GetErrorMessage(ViewModel.SelectedAccount!);
                                }
                                @if (!string.IsNullOrEmpty(errMsg))
                                {
                                    <div
                                        class="mx-3 mb-3 p-2 bg-bg-danger/10 border border-border-l1 rounded-lg text-xs text-span-error">
                                        <div class="flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                 class="h-4 w-4 mr-1.5 flex-shrink-0 mt-0.5" viewBox="0 0 20 20"
                                                 fill="currentColor">
                                                <path fill-rule="evenodd"
                                                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                                      clip-rule="evenodd"/>
                                            </svg>
                                            <span>@errMsg</span>
                                        </div>
                                    </div>
                                }
                            </div>

                            <!-- Autograph Status UI with provided template structure -->
                            <div class="mb-8 mt-4 px-1"> <!-- Header with aligned design -->
                                <div class="mb-4 flex flex-row items-center justify-between">
                                    <div class="flex flex-col">
                                        <h3 class="font-jakarta text-sm font-bold tracking-wide text-span-default">
                                            Autograph Status</h3>
                                        <div class="flex items-center mt-1">
                                            @if (AutoGraphServiceInstance.IsAccountRunning(ViewModel.SelectedAccount!))
                                            {
                                                <div class="h-1.5 w-1.5 rounded-full bg-text-main animate-pulse"></div>
                                                <span
                                                    class="ml-1.5 text-[10px] font-medium tracking-wide text-span-muted">Process running</span>
                                            }
                                            else
                                            {
                                                <div class="h-1.5 w-1.5 rounded-full bg-span-muted"></div>
                                                <span
                                                    class="ml-1.5 text-[10px] font-medium tracking-wide text-span-muted">Process idle</span>
                                            }
                                        </div>
                                    </div>

                                    <!-- Time counter with status information -->
                                    <div
                                        class="flex items-center bg-bg-surface border border-border-l1 rounded-lg px-2.5 py-1.5 shadow-sm">
                                        <div class="flex items-center space-x-2">
                                            <div class="flex items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                                     fill="currentColor" class="h-3.5 w-3.5 text-span-muted mr-1">
                                                    <path fill-rule="evenodd"
                                                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                                                          clip-rule="evenodd"/>
                                                </svg>
                                                <span
                                                    class="font-mono text-xs font-semibold @(AutoGraphServiceInstance.IsAccountRunning(ViewModel.SelectedAccount!) ? "text-green-400" : "text-span-default")">
                                                    @(FormatTimeSpan(ViewModel.SelectedAccount!.ElapsedTime))
                                                </span>
                                            </div>

                                            <div class="h-3 w-px bg-border-l1"></div>

                                            <div class="flex items-center">
                                                <span class="text-xs font-medium text-span-muted">Current:</span>
                                                <span class="ml-1.5 text-xs font-semibold text-span-default">
                                                    @(ViewModel.SelectedAccount!.CurrentStatus)
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Progress section with simpler design -->
                                <div class="mb-4">
                                    <div class="mb-2 flex items-center justify-between">
                                        <div class="flex items-center">
                                            <span class="text-xs font-medium text-span-muted">Progress</span>
                                            <div
                                                class="ml-2 rounded-full bg-bg-surface-hover px-2 py-0.5 text-[10px] font-medium text-span-default">
                                                @(Math.Round(ViewModel.SelectedAccount!.CooldownProgress, 0))% Complete
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-1">
                                            <span class="text-[10px] font-medium text-span-muted">Target:</span>
                                            <span
                                                class="text-[10px] font-medium text-span-default">@ViewModel.SelectedAccount!.TargetUsername</span>
                                        </div>
                                    </div>

                                    <!-- Simplified progress bar -->
                                    <div
                                        class="h-2 w-full overflow-hidden rounded-full bg-bg-surface border border-border-l1 shadow-inner">
                                        <div class="h-full rounded-full bg-text-main transition-all duration-300"
                                             style="width: @(ViewModel.SelectedAccount!.CooldownProgress)%;"></div>
                                    </div>
                                </div>

                                <!-- Stats section - simplified to 3 columns -->
                                <div class="grid grid-cols-3 gap-3">
                                    <div
                                        class="flex flex-col rounded-lg border border-border-l1 bg-bg-surface p-2 shadow-sm">
                                        <span class="text-[10px] font-medium text-span-muted">Sent</span>
                                        <span
                                            class="text-sm font-medium text-span-default">@(ViewModel.SelectedAccount!.SuccessCount + ViewModel.SelectedAccount!.FailureCount)</span>
                                    </div>
                                    <div
                                        class="flex flex-col rounded-lg border border-border-l1 bg-bg-surface p-2 shadow-sm">
                                        <span class="text-[10px] font-medium text-span-muted">Successful</span>
                                        <span
                                            class="text-sm font-medium text-span-default">@ViewModel.SelectedAccount!.SuccessCount</span>
                                    </div>
                                    <div
                                        class="flex flex-col rounded-lg border border-border-l1 bg-bg-surface p-2 shadow-sm">
                                        <span class="text-[10px] font-medium text-span-muted">Failed</span>
                                        <span
                                            class="text-sm font-medium text-span-default">@ViewModel.SelectedAccount!.FailureCount</span>
                                    </div>
                                </div>

                                <!-- Next run info -->
                                <div
                                    class="mt-3 flex items-center rounded-lg border border-border-l1 bg-bg-surface p-2.5 shadow-sm">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                                             class="h-3.5 w-3.5 text-span-muted mr-1">
                                            <path fill-rule="evenodd"
                                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                                                  clip-rule="evenodd"/>
                                        </svg>
                                        <span class="text-xs text-span-muted">Next run in:</span>
                                        <span
                                            class="ml-1 text-xs font-medium text-span-default">@FormatTimeSpan(ViewModel.SelectedAccount!.RemainingCooldown)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<DeveloperPanel IsDeveloperModeEnabled="IsDeveloperModeEnabled" OnToggleDeveloperMode="ToggleDeveloperMode"
                InitializationScreen="InitializationScreen"/>
