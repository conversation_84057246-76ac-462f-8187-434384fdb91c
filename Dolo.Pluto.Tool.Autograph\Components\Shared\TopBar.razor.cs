using Dolo.Pluto.Shard;
using Microsoft.AspNetCore.Components;
using System.Threading.Tasks;
using Dolo.Pluto.Shard.Configuration;

namespace Dolo.Pluto.Tool.Autograph.Components.Shared;

public partial class TopBar : ComponentBase
{
    [Inject] private IAppConfiguration AppConfiguration { get; set; } = default!;
    [Parameter]
    public EventCallback OnToggleDeveloperMode { get; set; }

    [Parameter]
    public EventCallback OnStopAllAutographs { get; set; }

    private async Task ToggleDeveloperMode()
    {
        if (OnToggleDeveloperMode.HasDelegate)
        {
            await OnToggleDeveloperMode.InvokeAsync();
        }
    }

    private async Task StopAllAutographs()
    {
        if (OnStopAllAutographs.HasDelegate)
        {
            await OnStopAllAutographs.InvokeAsync();
        }
    }
}
