<Project Sdk="Microsoft.NET.Sdk.Razor">
    <Import Condition=" '$(EAZFUSCATOR_NET_HOME)' != '' and Exists('$(EAZFUSCATOR_NET_HOME)\Integration\MSBuild\Eazfuscator.NET.targets') " Project="$(EAZFUSCATOR_NET_HOME)\Integration\MSBuild\Eazfuscator.NET.targets"/>
    <PropertyGroup>
        <TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net10.0-windows10.0.19041.0</TargetFrameworks>
        <!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
        <!-- <TargetFrameworks>net10.0-windows10.0.19041.0;net10.0-tizen</TargetFrameworks> -->
        <!-- Note for MacCatalyst:
                The default runtime is maccatalyst-x64, except in Release config, in which case the default is maccatalyst-x64;maccatalyst-arm64.
                When specifying both architectures, use the plural <RuntimeIdentifiers> instead of the singular <RuntimeIdentifier>.
                The Mac App Store will NOT accept apps with ONLY maccatalyst-arm64 indicated;
                either BOTH runtimes must be indicated or ONLY macatalyst-x64. -->
        <!-- For example: <RuntimeIdentifiers>maccatalyst-x64;maccatalyst-arm64</RuntimeIdentifiers> -->
        <OutputType>Exe</OutputType>
        <RootNamespace>Dolo.Pluto.Tool.Autograph</RootNamespace>
        <UseMaui>true</UseMaui>
        <SingleProject>true</SingleProject>
        <ImplicitUsings>enable</ImplicitUsings>
        <EnableDefaultCssItems>false</EnableDefaultCssItems>
        <Nullable>enable</Nullable>
        <!-- Display name -->
        <ApplicationTitle>Dolo.Pluto.Tool.Autograph</ApplicationTitle>
        <!-- App Identifier -->
        <ApplicationId>com.companyname.dolo.pluto.tool.autograph</ApplicationId>
        <!-- Versions -->
        <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
        <ApplicationVersion>1</ApplicationVersion>
        <!-- To develop, package, and publish an app to the Microsoft Store, see: https://aka.ms/MauiTemplateUnpackaged -->
        <WindowsPackageType>None</WindowsPackageType>
        <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
        <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.0</SupportedOSPlatformVersion>
        <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">24.0</SupportedOSPlatformVersion>
        <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
        <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
        <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion>
    </PropertyGroup>
    <PropertyGroup>
        <!-- Eazfuscator.NET is integrated with this project at MSBuild level: https://help.gapotchenko.com/eazfuscator.net/kb/100036 -->
        <EazfuscatorIntegration>MSBuild</EazfuscatorIntegration>
        <EazfuscatorActiveConfiguration>Release</EazfuscatorActiveConfiguration>
        <EazfuscatorCompatibilityVersion>2025.1</EazfuscatorCompatibilityVersion>
    </PropertyGroup>
    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
        <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    </PropertyGroup>
    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
        <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    </PropertyGroup>
    <ItemGroup>
        <!-- App Icon -->
        <MauiIcon Include="Resources\AppIcon\appicon.png"/>
        <!-- Splash Screen -->
        <MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128"/>
        <!-- Images -->
        <MauiImage Include="Resources\Images\*"/>
        <MauiImage Update="Resources\Images\dotnet_bot.svg" BaseSize="168,208"/>
        <!-- Custom Fonts -->
        <MauiFont Include="Resources\Fonts\*"/>
        <!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
        <MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)"/>
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.SignalR.Client.Core" Version="10.0.0-preview.6.25358.103"/>
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="10.0.0-preview.6.25358.103"/>
        <PackageReference Include="Microsoft.Maui.Controls" Version="$(MauiVersion)"/>
        <PackageReference Include="Microsoft.AspNetCore.Components.WebView.Maui" Version="$(MauiVersion)"/>
        <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="10.0.0-preview.6.25358.103"/>
        <PackageReference Include="Pacem.Extensions.Logging.File" Version="0.9.0-abel"/>
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\Dolo.Planet\Dolo.Planet.csproj"/>
        <ProjectReference Include="..\Dolo.Pluto.Shard\Dolo.Pluto.Shard.csproj"/>
        <ProjectReference Include="..\Dolo\Dolo.csproj"/>
    </ItemGroup>

    <Target Name="PostBuild" AfterTargets="PostBuildEvent" Condition="'$(Configuration)' == 'Release'">
        <Exec Command="cmd /c &quot;&quot;C:\Users\<USER>\Desktop\Projects\Dolo\obfuscate.bat&quot; -ProjectSubdir &quot;$(ProjectName)&quot; -DllName &quot;$(ProjectName)&quot; -BuildConfig &quot;$(Configuration)&quot; -Framework &quot;$(TargetFramework)&quot; -Rid &quot;$(RuntimeIdentifier)&quot; &gt; &quot;$(ProjectDir)obfuscation-build.log&quot; 2&gt;&amp;1&quot;"/>
    </Target>
</Project>







