﻿using System.Runtime.InteropServices;
using Dolo.Pluto.Shard.Extensions;
using Dolo.Pluto.Shard.Services.Initialization;
using Dolo.Pluto.Tool.Autograph.Models;
using Dolo.Pluto.Tool.Autograph.Services;
using Microsoft.Extensions.Logging;

namespace Dolo.Pluto.Tool.Autograph;

public static partial class MauiProgram
{
    [LibraryImport("kernel32.dll")]
    [return: MarshalAs(UnmanagedType.Bool)]
    private static partial bool AllocConsole();

    public static MauiApp CreateMauiApp()
    {
#if !RELEASE
        AllocConsole();
#endif

        var config = new AutographConfiguration();
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp<App>()
            .ConfigureSharedWebHost(config)
            .ConfigureFonts(fonts => { fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular"); });
        builder.Services.AddSharedServices(config);
        builder.Services.AddAutoServices();
        builder.Services.AddSharedUIComponents();
        builder.Services.AddInitializationServices<MainService>();

        builder.Services.AddMauiBlazorWebView();
        builder.Services.AddMainViewModel();
        builder.Services.AddLogging(logging =>
        {
            logging.ClearProviders();
            logging.AddConsole();
            logging.AddDebug();
            logging.SetMinimumLevel(LogLevel.Information);
        });

#if DEBUG
        builder.Services.AddBlazorWebViewDeveloperTools();
        // Create and register the DebugLogService as a singleton
        var debugLogService = new DebugLogService();
        builder.Services.AddSingleton(debugLogService);

        // Register our custom logger provider that forwards logs to the DebugLogService
        builder.Logging.AddProvider(new DebugLoggerProvider(debugLogService));

        builder.Logging.AddDebug();

#endif

        AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
        {
            Console.WriteLine($"Unhandled exception: {e.ExceptionObject}");
        };

        return builder.Build();
    }
}