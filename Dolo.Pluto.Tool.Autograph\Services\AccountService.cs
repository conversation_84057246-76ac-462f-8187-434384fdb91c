using Dolo.Planet;
using Dolo.Planet.Enums;
using Dolo.Pluto.Shard.Services;
using Dolo.Pluto.Tool.Autograph.Helpers;
using Dolo.Pluto.Tool.Autograph.Models;
using Microsoft.Extensions.Logging;

namespace Dolo.Pluto.Tool.Autograph.Services;

public class AccountService : IService
{
    private readonly List<Account> _accounts = new();
    private readonly ILogger<AccountService> _logger;
    private readonly SynchronizationContext? _syncContext;

    public AccountService(ILogger<AccountService> logger)
    {
        _logger = logger;
        _syncContext = SynchronizationContext.Current;
    }

    public IReadOnlyList<Account> Accounts => _accounts.AsReadOnly();

    public event Action? OnAccountsChanged;

    /// <summary>
    ///     Attempts to log into the MSP account with the provided credentials.
    /// </summary>
    /// <param name="username">The account username</param>
    /// <param name="password">The account password</param>
    /// <param name="server">The server to connect to</param>
    /// <param name="proxyString">Optional proxy string</param>
    /// <returns>A LoginValidationResult indicating success or failure</returns>
    public async Task<LoginValidationResult> LoginAsync(string username, string password, Server server,
        string? proxyString)
    {
        try
        {
            // Configure the MSP client
            var mspClient = new MspClient(config =>
            {
                config.Username = username;
                config.Password = password;
                config.Server = server;

                // Configure proxy if provided
                if (!string.IsNullOrWhiteSpace(proxyString))
                    config.CrossProxy = ProxyHelper.CreateProxyFromString(proxyString);

                //config.UseLogger(a => a.AddConsole().AddDebug().SetMinimumLevel(LogLevel.Debug));
                config.UseOriginalBehaviour();
            }); // Attempt to login
            var loginResult = await mspClient.LoginAsync();

            if (loginResult.Success && loginResult.Actor != null)
            {
                var lastAutographTime = loginResult.Actor.LastAutographAt;
                var timeSinceLastAutograph =
                    lastAutographTime.HasValue ? DateTime.Now - lastAutographTime.Value : TimeSpan.Zero;

                _logger.LogInformation(
                    "LOGIN DEBUG: Account {Username} LastAutographAt: {LastAutographAt}, CanSendAutograph: {CanSend}, RemainingTime: {RemainingTime}, Progress: {Progress}%",
                    username,
                    lastAutographTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "Never",
                    loginResult.Actor.CanSendAutograph,
                    loginResult.Actor.RemainingAutograph.ToString(@"hh\:mm\:ss"),
                    loginResult.Actor.ElapsedAutographPercentage);

                // Add more detailed cooldown information for debugging
                if (lastAutographTime.HasValue)
                    _logger.LogDebug(
                        "COOLDOWN DEBUG: Account {Username} TimeSinceLastAutograph: {TimeSince}, LastAutographAt UnixTimestamp: {UnixTimestamp}, DateTime.Now: {CurrentTime}",
                        username,
                        timeSinceLastAutograph.ToString(@"hh\:mm\:ss"),
                        new DateTimeOffset(lastAutographTime.Value).ToUnixTimeSeconds(),
                        DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            }

            return loginResult.Success
                ? LoginValidationResult.Success(mspClient, loginResult.Actor)
                : LoginValidationResult.Failure($"Login failed: {loginResult.Status}");
        }
        catch (Exception ex)
        {
            return LoginValidationResult.Failure($"Error: {ex.Message}");
        }
    }

    public async Task<bool> AddAccountAsync(Account account)
    {
        try
        {
            // Check if an account with the same username and server already exists
            if (_accounts.Any(a => a.Username.Equals(account.Username, StringComparison.OrdinalIgnoreCase) &&
                                   a.Server == account.Server))
            {
                _logger.LogWarning("Account already exists: {Username} on {Server}", account.Username, account.Server);
                return false; // Account already exists
            }

            // Simulate a network delay in development
            await Task.Delay(100);

            // In a real implementation, this would call an API or use a local database
            _accounts.Add(account);

            // Notify subscribers that the account list has changed using the safe method
            RaiseAccountsChanged();

            _logger.LogInformation("Successfully added account: {Username} on {Server}", account.Username,
                account.Server);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding account {Username} on {Server}", account.Username, account.Server);
            return false;
        }
    }

    public async Task<bool> AddAccountsAsync(IEnumerable<Account> accounts)
    {
        try
        {
            // Simulate network delay in development
            await Task.Delay(1500);

            // Filter out accounts that already exist (same username and server)
            var newAccounts = accounts.Where(newAccount =>
                !_accounts.Any(existingAccount =>
                    existingAccount.Username.Equals(newAccount.Username, StringComparison.OrdinalIgnoreCase) &&
                    existingAccount.Server == newAccount.Server
                )
            ).ToList();

            // Don't add any accounts if all are duplicates
            if (newAccounts.Count == 0)
            {
                _logger.LogWarning("No new accounts to add - all accounts already exist");
                return false;
            }

            // In a real implementation, this would call an API or use a local database
            _accounts.AddRange(newAccounts);

            // Notify subscribers that the account list has changed using the safe method
            RaiseAccountsChanged();

            _logger.LogInformation("Added {Count} new accounts", newAccounts.Count);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding multiple accounts");
            return false;
        }
    }

    public async Task<bool> RemoveAccountAsync(string id)
    {
        try
        {
            // Simulate a network delay in development
            await Task.Delay(100);

            var account = _accounts.Find(a => a.Id == id);

            if (account == null)
            {
                _logger.LogWarning("Attempted to remove account with ID {Id} but it was not found", id);
                return false;
            }

            _accounts.Remove(account);

            // Notify subscribers that the account list has changed using the safe method
            RaiseAccountsChanged();

            _logger.LogInformation("Removed account: {Username} on {Server}", account.Username, account.Server);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing account with ID {Id}", id);
            return false;
        }
    }

    /// <summary>
    ///     Tests all accounts to verify their credentials are still valid.
    /// </summary>
    /// <returns>A tuple with the number of valid and invalid accounts</returns>
    public async Task<(int validCount, int invalidCount)> VerifyAllAccountsAsync()
    {
        var validCount = 0;
        var invalidCount = 0;
        // Iterate over a copy of the list because RemoveAccountAsync modifies the underlying _accounts collection
        var accountsToCheck = new List<Account>(_accounts);

        foreach (var account in accountsToCheck)
        {
            var result = await LoginAsync(account.Username, account.Password, account.Server, account.ProxyString);

            if (result.IsValid)
            {
                validCount++;
            }
            else
            {
                invalidCount++;
                // RemoveAccountAsync will internally handle removing the account from _accounts
                // and invoking OnAccountsChanged.
                await RemoveAccountAsync(account.Id);
            }
        }

        // The explicit removal loop and OnAccountsChanged invocation are no longer needed here,
        // as RemoveAccountAsync handles these for each invalid account.

        return (validCount, invalidCount);
    }

    /// <summary>
    ///     Checks if an account with the specified username and server already exists.
    /// </summary>
    /// <param name="username">The username to check</param>
    /// <param name="server">The server to check</param>
    /// <returns>True if the account exists, false otherwise</returns>
    public bool AccountExists(string username, Server server)
    {
        return _accounts.Any(a =>
            a.Username.Equals(username, StringComparison.OrdinalIgnoreCase) &&
            a.Server == server);
    }

    /// <summary>
    ///     Safely raises the OnAccountsChanged event on the UI thread
    /// </summary>
    private void RaiseAccountsChanged()
    {
        if (OnAccountsChanged == null)
            return;

        // If we have a synchronization context (UI thread), post the event to it
        if (_syncContext != null)
            _syncContext.Post(_ => OnAccountsChanged?.Invoke(), null);
        else
            // Otherwise just invoke directly (we're already on the UI thread or in a test)
            OnAccountsChanged?.Invoke();
    }
}