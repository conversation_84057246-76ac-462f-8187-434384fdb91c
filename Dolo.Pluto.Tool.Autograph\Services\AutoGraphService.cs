using Dolo.Pluto.Shard.Services;
using Dolo.Pluto.Tool.Autograph.Helpers;
using Dolo.Pluto.Tool.Autograph.Models;

namespace Dolo.Pluto.Tool.Autograph.Services;

/// <summary>
///     Service for managing multiple concurrent autograph operations.
/// </summary>
public class AutoGraphService : IService
{
    private readonly Dictionary<string, DateTime> _processStartTimes = new();

    /// <summary>
    ///     Event that fires when autograph statistics are updated.
    /// </summary>
    public event Action? OnStatsUpdated;

    /// <summary>
    ///     Event that fires when there's an error in the autograph process.
    /// </summary>
    public event Action<string>? OnError;

    /// <summary>
    ///     Event that fires when an HTTP request is sent.
    /// </summary>
    public event EventHandler<RequestEventArgs>? RequestSent;

    /// <summary>
    ///     Checks if a specific account is currently running an autograph process.
    /// </summary>
    /// <param name="account">The account to check</param>
    /// <returns>True if this account is running the autograph process, false otherwise</returns>
    public bool IsAccountRunning(Account? account)
    {
        if (account == null)
            return false;

        return account.IsRunning;
    }

    /// <summary>
    ///     Starts the autograph process for the specified account.
    /// </summary>
    /// <param name="account">The account to use for sending autographs</param>
    /// <returns>True if started successfully, false otherwise</returns>
    public Task<bool> StartAsync(Account account)
    {
        if (account == null || account.MspClient == null || account.MspActor == null)
        {
            HandleError("Invalid account");
            return Task.FromResult(false);
        } // Generate a unique ID for this autograph session

        var sessionId = Guid.NewGuid().ToString().Substring(0, 8);

        // Log the start of the process
        RequestSent?.Invoke(this, new RequestEventArgs
        {
            Endpoint = "AUTOGRAPH",
            Message = "Initializing autograph process...",
            IsSuccess = true,
            RequestId = account.Username
        });
        if (account.IsRunning)
        {
            RequestSent?.Invoke(this, new RequestEventArgs
            {
                Endpoint = "AUTOGRAPH",
                Message = "Process already running for this account",
                IsSuccess = false,
                RequestId = account.Username
            });
            return Task.FromResult(false);
        }

        if (string.IsNullOrWhiteSpace(account.TargetUsername))
        {
            HandleError("Target username cannot be empty", account);
            RequestSent?.Invoke(this, new RequestEventArgs
            {
                Endpoint = "AUTOGRAPH",
                Message = "Target username cannot be empty",
                IsSuccess = false,
                RequestId = account.Username
            });
            return Task.FromResult(false);
        } // Log the preparation of the request

        RequestSent?.Invoke(this, new RequestEventArgs
        {
            Endpoint = "AUTOGRAPH",
            Message = $"Preparing to send autograph to: {account.TargetUsername}",
            IsSuccess = true,
            RequestId = account.Username
        }); // Reset the cancellation token source
        account.CancellationTokenSource = new CancellationTokenSource(); // Record when the process started
        account.ProcessStartTime = DateTime.Now;
        account.IsRunning = true;
        // Clear error message when starting a new process
        account.LastErrorMessage = string.Empty;
        // Reset the stored elapsed time to start fresh
        account.StoredElapsedTime = TimeSpan.Zero;
        // Set initial status
        account.CurrentStatus = "Starting..."; // Log the process start
        RequestSent?.Invoke(this, new RequestEventArgs
        {
            Endpoint = "AUTOGRAPH",
            Message = $"Starting autograph process for {account.Username} → {account.TargetUsername}",
            IsSuccess = true,
            RequestId = account.Username
        });
        // Log initial cooldown status
        if (account.MspActor != null && !account.MspActor.CanSendAutograph)
        {
            var remainingTime = account.MspActor.RemainingAutograph;
            var percentage = account.MspActor.ElapsedAutographPercentage;
            var lastAutographAt = account.MspActor.LastAutographAt;

            // Update the current status to show cooldown
            account.CurrentStatus = $"Cooldown: {FormatTimeSpan(remainingTime)}";

            var lastSentInfo = lastAutographAt.HasValue
                ? $" (Last sent: {lastAutographAt.Value:HH:mm:ss})"
                : "";

            RequestSent?.Invoke(this, new RequestEventArgs
            {
                Endpoint = "AUTOGRAPH",
                Message =
                    $"Initial cooldown status: {FormatTimeSpan(remainingTime)} remaining ({100 - percentage}% complete){lastSentInfo}",
                IsSuccess = true,
                RequestId = account.Username
            });
        }

        // Start the autograph process in a background task
        _ = Task.Run(() => RunAutographProcessAsync(account, sessionId));

        OnStatsUpdated?.Invoke();
        return Task.FromResult(true);
    }

    /// <summary>
    ///     Stops the autograph process for the specified account.
    /// </summary>
    /// <param name="account">The account to stop</param>
    public async Task StopAsync(Account account)
    {
        if (account == null || !account.IsRunning || account.CancellationTokenSource == null)
            return;
        var sessionId = Guid.NewGuid().ToString().Substring(0, 8);

        // Log stopping
        RequestSent?.Invoke(this, new RequestEventArgs
        {
            Endpoint = "AUTOGRAPH",
            Message = $"Stopping autograph process for {account.Username}",
            IsSuccess = true,
            RequestId = account.Username
        }); // Cancel the operation
        account.CancellationTokenSource.Cancel();
        await Task.Delay(100); // Small delay to allow cancel to propagate

        // Store the current elapsed time before stopping
        if (account.ProcessStartTime != DateTime.MinValue)
            account.StoredElapsedTime += DateTime.Now - account.ProcessStartTime;

        account.IsRunning = false;
        // Reset process start time when stopping
        account.ProcessStartTime = DateTime.MinValue;
        // Reset status
        account.CurrentStatus = "Idle";

        OnStatsUpdated?.Invoke();

        // Log stopped
        RequestSent?.Invoke(this, new RequestEventArgs
        {
            Endpoint = "AUTOGRAPH",
            Message = $"Autograph process stopped for {account.Username}",
            IsSuccess = true,
            RequestId = account.Username
        });
    }

    /// <summary>
    ///     Resets statistics for a specific account.
    /// </summary>
    /// <param name="account">The account to reset statistics for</param>
    public void ResetStatistics(Account account)
    {
        if (account == null)
            return;

        account.SuccessCount = 0;
        account.FailureCount = 0;
        account.TotalFame = 0;
        account.LastErrorMessage = string.Empty;
        account.StoredElapsedTime = TimeSpan.Zero;
        OnStatsUpdated?.Invoke();
    }

    private async Task RunAutographProcessAsync(Account account, string sessionId)
    {
        if (account.CancellationTokenSource == null)
            return;

        // Log initial LastAutographAt state for debugging
        if (account.MspActor != null)
        {
            var lastAutographTime = account.MspActor.LastAutographAt;
            var canSendAutograph = account.MspActor.CanSendAutograph;
            var remainingTime = account.MspActor.RemainingAutograph;
            var cooldownPercentage = account.MspActor.ElapsedAutographPercentage;

            RequestSent?.Invoke(this, new RequestEventArgs
            {
                Endpoint = "AUTOGRAPH_DEBUG",
                Message =
                    $"Initial state: LastAutographAt: {lastAutographTime?.ToString("yyyy-MM-dd HH:mm:ss.fff") ?? "Never"}, " +
                    $"CanSendAutograph: {canSendAutograph}, " +
                    $"RemainingTime: {remainingTime:hh\\:mm\\:ss}, " +
                    $"Cooldown: {cooldownPercentage}%",
                IsSuccess = true,
                RequestId = account.Username
            });
        }

        // Log sending request
        RequestSent?.Invoke(this, new RequestEventArgs
        {
            Endpoint = "AUTOGRAPH",
            Message = $"Sending autograph request for {account.Username} → {account.TargetUsername}",
            IsSuccess = true,
            RequestId = account.Username
        });

        try
        {
            await AutoGraphHelper.RunAutographProcessAsync(
                account,
                account.TargetUsername,
                account.CancellationTokenSource.Token,
                result => HandleResult(account, result, sessionId),
                error => HandleError(error, account, sessionId)
            ); // Log completion
            RequestSent?.Invoke(this, new RequestEventArgs
            {
                Endpoint = "AUTOGRAPH",
                Message = $"Autograph process completed for {account.Username}",
                IsSuccess = true,
                RequestId = account.Username
            });
        }
        catch (Exception ex)
        {
            // Log exception
            RequestSent?.Invoke(this, new RequestEventArgs
            {
                Endpoint = "AUTOGRAPH",
                Message = $"ERROR: {ex.Message}",
                IsSuccess = false,
                RequestId = account.Username
            });
        }
        finally
        {
            account.IsRunning = false;
            OnStatsUpdated?.Invoke();
        }
    }

    private void HandleResult(Account account, AutoGraphResult result, string sessionId)
    {
        switch (result.Status)
        {
            case AutoGraphStatus.Success:
                account.SuccessCount++;
                account.TotalFame += result.Fame;
                // Update status
                account.CurrentStatus = "Autograph Sent.";

                // Log success with LastAutographAt timing information
                var lastAutographTime = account.MspActor?.LastAutographAt;
                var lastAutographInfo = lastAutographTime.HasValue
                    ? $" (Last sent: {lastAutographTime.Value:HH:mm:ss})"
                    : "";

                // Add detailed debug information for the LastAutographAt property
                if (lastAutographTime.HasValue)
                {
                    var timeSinceAutograph = DateTime.Now - lastAutographTime.Value;
                    RequestSent?.Invoke(this, new RequestEventArgs
                    {
                        Endpoint = "AUTOGRAPH_DEBUG",
                        Message =
                            $"LastAutographAt detailed info: {lastAutographTime.Value:yyyy-MM-dd HH:mm:ss.fff}, Unix: {new DateTimeOffset(lastAutographTime.Value).ToUnixTimeSeconds()}, TimeSince: {timeSinceAutograph.TotalSeconds:0.00}s",
                        IsSuccess = true,
                        RequestId = account.Username
                    });
                }

                RequestSent?.Invoke(this, new RequestEventArgs
                {
                    Endpoint = "AUTOGRAPH",
                    Message =
                        $"SUCCESS: Autograph sent to {account.TargetUsername}. Gained {result.Fame} fame.{lastAutographInfo}",
                    IsSuccess = true,
                    RequestId = account.Username
                });
                break;
            case AutoGraphStatus.Cooldown:
                // Get last autograph time
                var lastSentTime = account.MspActor?.LastAutographAt;
                var timeSinceLastFormat = lastSentTime.HasValue
                    ? $" (Sent {DateTime.Now - lastSentTime.Value:hh\\:mm\\:ss} ago at {lastSentTime.Value:HH:mm:ss})"
                    : "";

                // Add detailed debug information for cooldown timing
                if (lastSentTime.HasValue)
                {
                    var timeSinceSent = DateTime.Now - lastSentTime.Value;
                    RequestSent?.Invoke(this, new RequestEventArgs
                    {
                        Endpoint = "COOLDOWN_DEBUG",
                        Message = $"Cooldown details: LastAutographAt: {lastSentTime.Value:yyyy-MM-dd HH:mm:ss.fff}, " +
                                  $"TimeSince: {timeSinceSent.TotalSeconds:0.00}s, " +
                                  $"RemainingAutograph: {result.FormattedCooldown}, " +
                                  $"CooldownPercentage: {result.CooldownPercentage}%, " +
                                  $"CanSend: {account.MspActor?.CanSendAutograph}",
                        IsSuccess = true,
                        RequestId = account.Username
                    });
                }

                // Update status with remaining time
                account.CurrentStatus = $"Cooldown: {result.FormattedCooldown}";

                // Update UI to reflect cooldown status with proper percentage display and timing information
                RequestSent?.Invoke(this, new RequestEventArgs
                {
                    Endpoint = "AUTOGRAPH",
                    Message =
                        $"COOLDOWN: {result.FormattedCooldown} remaining ({100 - result.CooldownPercentage}% complete){timeSinceLastFormat}",
                    IsSuccess = true,
                    RequestId = account.Username
                });
                break;
            case AutoGraphStatus.Failure:
                account.FailureCount++;

                // Update status to error
                account.CurrentStatus = "Autograph failed..";

                // Only persist error messages for specific error types                    // Check for "user not found" errors
                if (result.ErrorMessage.Contains("not found", StringComparison.OrdinalIgnoreCase) ||
                    result.ErrorMessage.Contains("user not found", StringComparison.OrdinalIgnoreCase))
                {
                    account.LastErrorMessage =
                        $"Target user '{account.TargetUsername}' was not found. Please check the username and try again.";

                    // Reset the account to idle state
                    account.IsRunning = false;
                    account.ProcessStartTime = DateTime.MinValue;
                    account.CurrentStatus = "Idle";
                }
                // Check for "cannot send to self" error
                else if (result.ErrorMessage.Contains("cannot send", StringComparison.OrdinalIgnoreCase) &&
                         result.ErrorMessage.Contains("yourself", StringComparison.OrdinalIgnoreCase))
                {
                    account.LastErrorMessage = "You cannot send an autograph to yourself.";

                    // Reset the account to idle state
                    account.IsRunning = false;
                    account.ProcessStartTime = DateTime.MinValue;
                    account.CurrentStatus = "Idle";
                }
                // For all other errors, log them but don't persist to the account
                else
                {
                    // Create a temporary error message for the log but don't update account.LastErrorMessage
                    var tempErrorMessage = result.ErrorMessage;
                    if (string.IsNullOrEmpty(tempErrorMessage))
                        tempErrorMessage =
                            $"Failed to send autograph to '{account.TargetUsername}' due to an unknown error.";

                    // Log failure but don't store in LastErrorMessage
                    RequestSent?.Invoke(this, new RequestEventArgs
                    {
                        Endpoint = "AUTOGRAPH",
                        Message = $"FAILED: {tempErrorMessage}",
                        IsSuccess = false,
                        RequestId = account.Username
                    });

                    break;
                }

                // Log the persistent error message
                RequestSent?.Invoke(this, new RequestEventArgs
                {
                    Endpoint = "AUTOGRAPH",
                    Message = $"FAILED: {account.LastErrorMessage}",
                    IsSuccess = false,
                    RequestId = account.Username
                });
                break;
        }

        OnStatsUpdated?.Invoke();
    }

    private void HandleError(string error, Account? account = null, string sessionId = "")
    {
        if (account != null)
        {
            // Only persist specific types of error messages
            if (error.Contains("not found", StringComparison.OrdinalIgnoreCase) ||
                error.Contains("user not found", StringComparison.OrdinalIgnoreCase) ||
                (error.Contains("cannot send", StringComparison.OrdinalIgnoreCase) &&
                 error.Contains("yourself", StringComparison.OrdinalIgnoreCase)))
            {
                account.LastErrorMessage = error;

                // Reset the account to idle state for these specific errors
                account.IsRunning = false;
                account.ProcessStartTime = DateTime.MinValue;
                account.CurrentStatus = "Idle";
            }

            // Always log the error, regardless of whether it's persisted
            if (!string.IsNullOrEmpty(sessionId))
                RequestSent?.Invoke(this, new RequestEventArgs
                {
                    Endpoint = "AUTOGRAPH",
                    Message = $"ERROR: {error}",
                    IsSuccess = false,
                    RequestId = account.Username
                });
        }

        OnError?.Invoke(error);
        OnStatsUpdated?.Invoke();
    }

    /// <summary>
    ///     Gets debug information about the cooldown state for an account.
    /// </summary>
    public string GetCooldownDebugInfo(Account account)
    {
        if (account?.MspActor == null)
            return "No account selected";

        var mspActor = account.MspActor;
        var remainingTime = mspActor.RemainingAutograph;
        var progress = mspActor.ElapsedAutographPercentage;
        var canSend = mspActor.CanSendAutograph;
        var lastAutographAt = mspActor.LastAutographAt;
        var vipTier = mspActor.VipTier;
        return canSend
            ? "Ready to send autograph (100% ready)"
            : $"Cooldown: {100 - progress}% complete ({FormatTimeSpan(remainingTime)} remaining)" +
              $"\nLast autograph sent: {lastAutographAt?.ToString() ?? "Never"}" +
              $"\nVIP Tier: {vipTier}";
    }

    /// <summary>
    ///     Helper method to format a TimeSpan for display
    /// </summary>
    private string FormatTimeSpan(TimeSpan timeSpan)
    {
        if (timeSpan.TotalHours >= 1)
            return $"{(int)timeSpan.TotalHours:D2}:{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
        if (timeSpan.TotalMinutes >= 1)
            return $"{timeSpan.Minutes:D2}:{timeSpan.Seconds:D2}";
        return $"00:{timeSpan.Seconds:D2}";
    }
}