using System.Collections.ObjectModel;
using System.Text;
using Dolo.Pluto.Shard.Services;
using Microsoft.Extensions.Logging;

namespace Dolo.Pluto.Tool.Autograph.Services;

public class LogEntry
{
    public DateTime Timestamp { get; set; } = DateTime.Now;
    public string Message { get; set; } = string.Empty;
    public LogLevel Level { get; set; }
    public string Category { get; set; } = string.Empty;
    public Exception? Exception { get; set; }

    public string LevelString => Level switch
    {
        LogLevel.Trace => "TRACE",
        LogLevel.Debug => "DEBUG",
        LogLevel.Information => "INFO",
        LogLevel.Warning => "WARN",
        LogLevel.Error => "ERROR",
        LogLevel.Critical => "CRIT",
        _ => "LOG"
    };

    public string Color => Level switch
    {
        LogLevel.Trace => "text-gray-400",
        LogLevel.Debug => "text-blue-300",
        LogLevel.Information => "text-green-300",
        LogLevel.Warning => "text-yellow-300",
        LogLevel.Error => "text-red-400",
        LogLevel.Critical => "text-red-500",
        _ => "text-gray-300"
    };
}

public class DebugLogService : IService
{
    private const int MaxLogEntries = 1000;
    private readonly ObservableCollection<LogEntry> _logs = new();

    public IReadOnlyList<LogEntry> Logs => _logs;

    public event Action? LogsChanged;

    public void AddLog(LogLevel level, string category, string message, Exception? exception = null)
    {
        var entry = new LogEntry
        {
            Timestamp = DateTime.Now,
            Level = level,
            Message = message,
            Category = category,
            Exception = exception
        };

        // Remove oldest logs if we exceed the limit
        while (_logs.Count >= MaxLogEntries) _logs.RemoveAt(_logs.Count - 1);

        // Add at the beginning to show newest logs first
        _logs.Insert(0, entry);

        LogsChanged?.Invoke();
    }

    public void Clear()
    {
        _logs.Clear();
        LogsChanged?.Invoke();
    }

    public IReadOnlyList<LogEntry> GetFilteredLogs(LogLevel minLevel = LogLevel.Trace, string? categoryFilter = null)
    {
        return _logs
            .Where(log => log.Level >= minLevel)
            .Where(log =>
                string.IsNullOrEmpty(categoryFilter) ||
                log.Category.Contains(categoryFilter, StringComparison.OrdinalIgnoreCase))
            .ToList();
    }

    /// <summary>
    ///     Exports the current logs to a text file.
    /// </summary>
    /// <param name="filePath">The path where the log file should be saved</param>
    /// <returns>True if the export was successful, false otherwise</returns>
    public async Task<bool> ExportLogsToFileAsync(string filePath)
    {
        try
        {
            var sb = new StringBuilder();
            sb.AppendLine($"Autograph Debug Logs - Exported {DateTime.Now}");
            sb.AppendLine("==================================================");
            sb.AppendLine();

            foreach (var log in _logs.OrderBy(l => l.Timestamp))
            {
                sb.AppendLine(
                    $"[{log.Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{log.LevelString}] [{log.Category}] {log.Message}");

                if (log.Exception != null)
                {
                    sb.AppendLine($"  Exception: {log.Exception.Message}");
                    if (log.Exception.StackTrace != null) sb.AppendLine($"  StackTrace: {log.Exception.StackTrace}");
                    sb.AppendLine();
                }
            }

            await File.WriteAllTextAsync(filePath, sb.ToString());
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }
}

// Custom logger provider that will forward logs to our DebugLogService
public class DebugLoggerProvider : ILoggerProvider
{
    private readonly DebugLogService _logService;

    public DebugLoggerProvider(DebugLogService logService)
    {
        _logService = logService;
    }

    public ILogger CreateLogger(string categoryName)
    {
        return new DebugLogger(_logService, categoryName);
    }

    public void Dispose()
    {
        // Nothing to dispose
    }

    private class DebugLogger : ILogger
    {
        private readonly string _category;
        private readonly DebugLogService _logService;

        public DebugLogger(DebugLogService logService, string category)
        {
            _logService = logService;
            _category = category;
        }

        public IDisposable? BeginScope<TState>(TState state) where TState : notnull
        {
            // We don't implement scopes in this basic logger
            return null;
        }

        public bool IsEnabled(LogLevel logLevel)
        {
            // For now, enable all log levels
            return true;
        }

        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception,
            Func<TState, Exception?, string> formatter)
        {
            if (!IsEnabled(logLevel))
                return;

            var message = formatter(state, exception);
            _logService.AddLog(logLevel, _category, message, exception);
        }
    }
}