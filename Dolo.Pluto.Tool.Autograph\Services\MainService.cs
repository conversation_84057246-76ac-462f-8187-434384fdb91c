using System;
using Dolo.Pluto.Shard;
using Dolo.Pluto.Shard.Services;
using Dolo.Pluto.Tool.Autograph.Components.Content;
using Dolo.Pluto.Tool.Autograph.Helpers;
using Dolo.Pluto.Tool.Autograph.Models;

namespace Dolo.Pluto.Tool.Autograph.Services;

public class MainService : IService, IMainService {
    private readonly AutoGraphService _autoGraphService;

    public Shard.Toolbox.Tool? Tool { get; set; }
    public Main Main { get; set; } = default!;
    private string? _selectedAccountId;

    public event Action? OnSelectedAccountChanged;
    public event Action<RequestLog>? RequestLogged;

    // Add constructor for dependency injection
    public MainService(AutoGraphService autoGraphService)
    {
        _autoGraphService = autoGraphService;

        // Subscribe to RequestSent events from AutoGraphService
        _autoGraphService.RequestSent += (sender, args) =>
        {
            // Convert RequestEventArgs to RequestLog and log it
            var log = RequestHelper.ConvertToRequestLog(args);
            LogRequest(log);
        };
    }

    public void SelectAccount(Account account)
    {
        if (account == null)
            return;

        _selectedAccountId = account.Id;
        OnSelectedAccountChanged?.Invoke();
    }

    public bool IsSelectedAccount(Account account)
    {
        if (account == null || _selectedAccountId == null)
            return false;

        return _selectedAccountId == account.Id;
    }

    // Method to log requests
    public void LogRequest(RequestLog log)
    {
        RequestLogged?.Invoke(log);
    }    // Method to reset the timer for an account
    public void ResetTimer(Account account)
    {
        if (account == null)
            return;

        account.StoredElapsedTime = TimeSpan.Zero;
    }    // Implementation of IMainService interface
    public void StateHasChangedAsync() {
        Main?.TriggerStateChanged();
    }
}
