﻿using System.Runtime.InteropServices;
using Dolo.Core.Interceptor.Services;
using Dolo.Pluto.Shard.Extensions;
using Dolo.Pluto.Shard.Services.Initialization;
using Dolo.Pluto.Tool.Charles.Services;
using Microsoft.Extensions.Logging;

namespace Dolo.Pluto.Tool.Charles;

public static class MauiProgram
{
    [DllImport("kernel32.dll")]
    private static extern bool AllocConsole();

    public static MauiApp CreateMauiApp()
    {
        AllocConsole();

        var config = new CharlesConfiguration();
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp<App>()
            .ConfigureSharedWebHost(config)
            .ConfigureFonts(fonts => { fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular"); });
        builder.Services.AddMauiBlazorWebView();
        builder.Services.AddSharedServices(config);

        // Register core interceptor services
        builder.Services.AddScoped<AmfService>();
        builder.Services.AddScoped<ResponseDecodingService>();
        // Register content processing services
        builder.Services.AddScoped<ContentProcessingService>();
        builder.Services.AddScoped<ContentCacheService>();

        // Auto-discover and register all IService implementations
        builder.Services.AddAutoServices();

        // Register initialization screen dependencies
        builder.Services.AddInitializationServices<MainService>();

#if DEBUG
        builder.Services.AddBlazorWebViewDeveloperTools();
        builder.Logging.AddDebug();
#endif

        return builder.Build();
    }
}