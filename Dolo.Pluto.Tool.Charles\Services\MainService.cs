using System;
using Dolo.Pluto.Shard;
using Dolo.Pluto.Shard.Services;
using Dolo.Pluto.Tool.Charles.Components.Pages;

namespace Dolo.Pluto.Tool.Charles.Services;

public class MainService : IService, IMainService
{
    public Shard.Toolbox.Tool? Tool { get; set; }
    public Main Main { get; set; } = default!;

    public void StateHasChangedAsync()
    {
        Main?.TriggerStateChanged();
    }
}
