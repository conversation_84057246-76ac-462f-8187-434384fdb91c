using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.Tool.Charles.Services;

public class SettingsService : IService
{
    private string _amfDisplayMode = "tree";
    private bool _amfExpandByDefault;
    private bool _amfPrettyPrint = true;
    private bool _autoSaveEnabled = true;
    private bool _breakpointsEnabled;
    private string _defaultExportFormat = "json";
    private int _maxSessionHistory = 1000;

    // Breakpoints settings
    public bool BreakpointsEnabled
    {
        get => _breakpointsEnabled;
        set
        {
            if (_breakpointsEnabled != value)
            {
                _breakpointsEnabled = value;
                BreakpointsToggled?.Invoke();
                SettingsChanged?.Invoke();
            }
        }
    }

    // AMF3 settings
    public bool AmfPrettyPrint
    {
        get => _amfPrettyPrint;
        set
        {
            _amfPrettyPrint = value;
            SettingsChanged?.Invoke();
        }
    }

    public bool AmfExpandByDefault
    {
        get => _amfExpandByDefault;
        set
        {
            _amfExpandByDefault = value;
            SettingsChanged?.Invoke();
        }
    }

    public string AmfDisplayMode
    {
        get => _amfDisplayMode;
        set
        {
            _amfDisplayMode = value;
            SettingsChanged?.Invoke();
        }
    }

    // General settings
    public bool AutoSaveEnabled
    {
        get => _autoSaveEnabled;
        set
        {
            _autoSaveEnabled = value;
            SettingsChanged?.Invoke();
        }
    }

    public int MaxSessionHistory
    {
        get => _maxSessionHistory;
        set
        {
            _maxSessionHistory = value;
            SettingsChanged?.Invoke();
        }
    }

    public string DefaultExportFormat
    {
        get => _defaultExportFormat;
        set
        {
            _defaultExportFormat = value;
            SettingsChanged?.Invoke();
        }
    }

    public event Action? SettingsChanged;
    public event Action? BreakpointsToggled;

    public void ToggleBreakpoints()
    {
        BreakpointsEnabled = !BreakpointsEnabled;
    }

    public void ResetToDefaults()
    {
        _breakpointsEnabled = false;
        _amfPrettyPrint = true;
        _amfExpandByDefault = false;
        _amfDisplayMode = "tree";
        _autoSaveEnabled = true;
        _maxSessionHistory = 1000;
        _defaultExportFormat = "json";
        SettingsChanged?.Invoke();
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}