using System.Collections.ObjectModel;
using Dolo.Pluto.Shard.Services;
using Dolo.Pluto.Tool.Charles.Models;

namespace Dolo.Pluto.Tool.Charles.Services;

public class TrafficDataService : IService
{
    private readonly List<TrafficSession> _allSessions = [];
    private readonly Dictionary<string, List<TrafficSession>> _hostGroups = [];
    private readonly TrafficInterceptorService _interceptorService;
    private readonly object _updateLock = new();
    private readonly Timer? _updateTimer;
    private string _currentFilter = string.Empty;
    private bool _hasPendingUpdate;
    private DateTime _lastUpdate = DateTime.MinValue;

    public TrafficDataService(TrafficInterceptorService interceptorService)
    {
        _interceptorService = interceptorService;
        _interceptorService.NewSession += OnNewSession;
        _interceptorService.TrafficUpdated += OnTrafficUpdated;

        _updateTimer = new Timer(OnTimerUpdate, null, Timeout.Infinite, Timeout.Infinite);
    }

    public TrafficSession? SelectedSession { get; private set; }

    public ReadOnlyCollection<TrafficSession> FilteredSessions { get; private set; } =
        new List<TrafficSession>().AsReadOnly();

    public ReadOnlyDictionary<string, List<TrafficSession>> HostGroups { get; private set; } =
        new Dictionary<string, List<TrafficSession>>().AsReadOnly();

    public event Action? DataUpdated;
    public event Action<TrafficSession>? SessionSelected;
    public event Action? FilterChanged;

    private void OnNewSession(TrafficSession session)
    {
        lock (_updateLock)
        {
            _allSessions.Add(session);
            ScheduleUpdate();
        }
    }

    private void OnTrafficUpdated()
    {
        lock (_updateLock)
        {
            _allSessions.Clear();
            _allSessions.AddRange(_interceptorService.Sessions);
            ScheduleUpdate();
        }
    }

    private void ScheduleUpdate()
    {
        if (_hasPendingUpdate)
            return;

        var timeSinceLastUpdate = DateTime.Now - _lastUpdate;
        var delay = timeSinceLastUpdate < TimeSpan.FromMilliseconds(100)
            ? TimeSpan.FromMilliseconds(100)
            : TimeSpan.Zero;

        _hasPendingUpdate = true;
        _updateTimer?.Change(delay, Timeout.InfiniteTimeSpan);
    }

    private void OnTimerUpdate(object? state)
    {
        lock (_updateLock)
        {
            if (!_hasPendingUpdate)
                return;

            UpdateHostGroups();
            UpdateFilteredSessions();
            _lastUpdate = DateTime.Now;
            _hasPendingUpdate = false;
        }

        DataUpdated?.Invoke();
    }

    private void UpdateHostGroups()
    {
        _hostGroups.Clear();

        var sessions = _allSessions.AsEnumerable();

        if (!string.IsNullOrWhiteSpace(_currentFilter))
        {
            var filterTerm = _currentFilter.ToLowerInvariant();
            sessions = sessions.Where(s =>
                GetSafeHost(s).ToLowerInvariant().Contains(filterTerm) ||
                s.Path.ToLowerInvariant().Contains(filterTerm) ||
                s.Method.ToLowerInvariant().Contains(filterTerm) ||
                s.StatusCode.ToString().Contains(filterTerm));
        }

        foreach (var session in sessions)
        {
            var host = GetSafeHost(session);
            if (!_hostGroups.TryGetValue(host, out var hostSessions))
            {
                hostSessions = [];
                _hostGroups[host] = hostSessions;
            }

            hostSessions.Add(session);
        }

        foreach (var group in _hostGroups.Values) group.Sort((a, b) => a.StartTime.CompareTo(b.StartTime));

        HostGroups = new ReadOnlyDictionary<string, List<TrafficSession>>(_hostGroups);
    }

    private string GetSafeHost(TrafficSession session)
    {
        if (session.Request?.Uri != null)
            try
            {
                return $"{session.Request.Uri.Scheme}://{session.Request.Uri.Host}";
            }
            catch
            {
                // Fallback for invalid URIs
            }

        return !string.IsNullOrEmpty(session.Hostname)
            ? $"{session.Protocol?.ToLower() ?? "http"}://{session.Hostname}"
            : "Unknown Host";
    }

    private void UpdateFilteredSessions()
    {
        var sessions = _allSessions.Where(s => IsValidSession(s));

        if (!string.IsNullOrWhiteSpace(_currentFilter))
        {
            var filterTerm = _currentFilter.ToLowerInvariant();
            sessions = sessions.Where(s =>
                GetSafeHost(s).ToLowerInvariant().Contains(filterTerm) ||
                s.Path.ToLowerInvariant().Contains(filterTerm) ||
                s.Method.ToLowerInvariant().Contains(filterTerm) ||
                s.StatusCode.ToString().Contains(filterTerm));
        }

        var filtered = sessions
            .OrderByDescending(s => s.StartTime)
            .ToList();

        FilteredSessions = filtered.AsReadOnly();
    }

    private static bool IsValidSession(TrafficSession session)
    {
        // Only include sessions that have meaningful data
        return !string.IsNullOrEmpty(session.Hostname) ||
               session.Request?.Uri != null ||
               (!string.IsNullOrEmpty(session.Method) && session.Method != "PENDING");
    }

    public void SelectSession(TrafficSession session)
    {
        SelectedSession = session;
        SessionSelected?.Invoke(session);
    }

    public void SelectSession(string sessionId)
    {
        var session = _allSessions.FirstOrDefault(s => s.Id == sessionId);
        if (session != null) SelectSession(session);
    }

    public void ClearData()
    {
        _allSessions.Clear();
        _hostGroups.Clear();
        _currentFilter = string.Empty;
        SelectedSession = null;
        UpdateHostGroups();
        UpdateFilteredSessions();
        DataUpdated?.Invoke();
    }

    public void SetFilter(string filterText)
    {
        if (_currentFilter != filterText)
        {
            _currentFilter = filterText ?? string.Empty;
            FilterChanged?.Invoke();
        }

        UpdateHostGroups();
        UpdateFilteredSessions();
        DataUpdated?.Invoke();
    }

    public List<TrafficSession> GetAllSessions()
    {
        return _allSessions.ToList();
    }

    public List<TrafficSession> GetSessionsForHost(string host)
    {
        return _hostGroups.TryGetValue(host, out var sessions) ? sessions.ToList() : [];
    }

    public TrafficSession? GetSessionById(string sessionId)
    {
        return _allSessions.FirstOrDefault(s => s.Id == sessionId);
    }

    public List<TrafficSession> SearchSessions(string searchTerm)
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
            return FilteredSessions.ToList();

        searchTerm = searchTerm.ToLowerInvariant();

        return _allSessions
            .Where(s =>
                GetSafeHost(s).ToLowerInvariant().Contains(searchTerm) ||
                s.Path.ToLowerInvariant().Contains(searchTerm) ||
                s.Method.ToLowerInvariant().Contains(searchTerm) ||
                s.StatusCode.ToString().Contains(searchTerm))
            .OrderByDescending(s => s.StartTime)
            .ToList();
    }

    public Dictionary<string, int> GetHostStatistics()
    {
        return _hostGroups.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.Count);
    }

    public Dictionary<int, int> GetStatusCodeStatistics()
    {
        return _allSessions
            .Where(s => s.Response != null)
            .GroupBy(s => s.StatusCode)
            .ToDictionary(g => g.Key, g => g.Count());
    }

    public Dictionary<string, int> GetMethodStatistics()
    {
        return _allSessions
            .GroupBy(s => s.Method)
            .ToDictionary(g => g.Key, g => g.Count());
    }

    public static string DecodeUrl(string url)
    {
        if (string.IsNullOrEmpty(url)) return url;

        try
        {
            return Uri.UnescapeDataString(url);
        }
        catch
        {
            return url;
        }
    }

    public static string DecodeUrlPath(string path)
    {
        if (string.IsNullOrEmpty(path)) return path;

        try
        {
            return Uri.UnescapeDataString(path);
        }
        catch
        {
            return path;
        }
    }

    public void Dispose()
    {
        _updateTimer?.Dispose();
        _interceptorService.NewSession -= OnNewSession;
        _interceptorService.TrafficUpdated -= OnTrafficUpdated;
    }
}