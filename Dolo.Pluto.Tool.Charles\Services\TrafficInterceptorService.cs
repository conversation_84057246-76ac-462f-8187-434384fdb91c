using System.Collections.Concurrent;
using System.Collections.ObjectModel;
using System.Net;
using Dolo.Core.Interceptor;
using Dolo.Core.Interceptor.Extensions;
using Dolo.Core.Interceptor.Models;
using Dolo.Pluto.Shard.Services;
using Dolo.Pluto.Tool.Charles.Models;

namespace Dolo.Pluto.Tool.Charles.Services;

public class TrafficInterceptorService : IService, IDisposable
{
    private readonly ConcurrentBag<AmfTrafficData> _amfTraffic = new();
    private readonly ConcurrentBag<CertificateInfo> _certificates = new();
    private readonly List<string> _errors = [];
    private readonly ConcurrentDictionary<string, TrafficSession> _sessions = new();
    private CancellationTokenSource? _cancellationTokenSource;
    private HttpsInterceptor? _interceptor;
    private Task? _interceptorTask;

    public InterceptorStatus Status { get; private set; } = new();
    public ReadOnlyCollection<TrafficSession> Sessions => _sessions.Values.ToList().AsReadOnly();
    public ReadOnlyCollection<AmfTrafficData> AmfTraffic => _amfTraffic.ToList().AsReadOnly();
    public ReadOnlyCollection<CertificateInfo> Certificates => _certificates.ToList().AsReadOnly();
    public ReadOnlyCollection<string> Errors => _errors.AsReadOnly();

    public void Dispose()
    {
        _ = StopInterceptorAsync();
        _cancellationTokenSource?.Dispose();
    }

    public event Action? TrafficUpdated;
    public event Action? StatusChanged;
    public event Action<TrafficSession>? NewSession;
    public event Action<AmfTrafficData>? NewAmfTraffic;
    public event Action<CertificateInfo>? CertificateGenerated;
    public event Action<string>? ErrorOccurred;

    public async Task<bool> StartInterceptorAsync(int port = 8888)
    {
        if (_interceptor != null) await StopInterceptorAsync().ConfigureAwait(false);

        try
        {
            var interceptorSettings = new InterceptorConfig()
                .ForMovieStarPlanet()
                .UsePort(port)
                .UseLogger(a => a.AddConsole().AddCertificate().AddSsl())
                .UseOCSP();

            _interceptor = new HttpsInterceptor(interceptorSettings);

            // Check for and clear invalid cached certificates
            if (_interceptor.HasInvalidCachedCertificates())
            {
                Console.WriteLine("⚠️ Found invalid cached certificates (missing private keys). Clearing cache...");
                _interceptor.ClearCertificateCache();
                Console.WriteLine("✅ Certificate cache cleared. New certificates will be generated with private keys.");
            }

            // Subscribe to the new transaction-based events
            _interceptor.TransactionCompleted += OnTransactionCompleted;
            _interceptor.TransactionTimedOut += OnTransactionTimedOut;
            _interceptor.CertificateGenerated += OnCertificateGenerated;
            _interceptor.ErrorOccurred += OnErrorOccurred;
            _cancellationTokenSource = new CancellationTokenSource();

            var installed = _interceptor.InstallRootCertificate();

            _interceptorTask = Task.Run(async () =>
            {
                try
                {
                    await _interceptor.StartAsync(_cancellationTokenSource.Token).ConfigureAwait(false);
                }
                catch (OperationCanceledException)
                {
                    // Expected when stopping
                }
                catch (Exception ex)
                {
                    var error = $"Interceptor task error: {ex.Message}";
                    _errors.Add(error);
                    Status = new InterceptorStatus
                    {
                        IsRunning = false,
                        LastError = error
                    };
                    ErrorOccurred?.Invoke(error);
                    StatusChanged?.Invoke();
                }
            }, _cancellationTokenSource.Token);

            await Task.Delay(200).ConfigureAwait(false);

            Status = new InterceptorStatus
            {
                IsRunning = true,
                IsRootCertificateInstalled = installed,
                ProxyEndpoint = $"127.0.0.1:{port}",
                TargetDomains = interceptorSettings.TargetDomains.ToList(),
                StartTime = DateTime.Now
            };

            StatusChanged?.Invoke();
            return true;
        }
        catch (Exception ex)
        {
            var error = $"Failed to start interceptor: {ex.Message}";
            _errors.Add(error);
            Status = new InterceptorStatus
            {
                IsRunning = false,
                LastError = error
            };
            ErrorOccurred?.Invoke(error);
            StatusChanged?.Invoke();
            return false;
        }
    }

    public async Task StopInterceptorAsync()
    {
        if (_interceptor != null)
            try
            {
                // Unsubscribe from transaction-based events
                _interceptor.TransactionCompleted -= OnTransactionCompleted;
                _interceptor.TransactionTimedOut -= OnTransactionTimedOut;
                _interceptor.CertificateGenerated -= OnCertificateGenerated;
                _interceptor.ErrorOccurred -= OnErrorOccurred;
                await _cancellationTokenSource!.CancelAsync().ConfigureAwait(false);
                await _interceptor.StopAsync();

                if (_interceptorTask != null)
                    try
                    {
                        await _interceptorTask.WaitAsync(TimeSpan.FromSeconds(2)).ConfigureAwait(false);
                    }
                    catch (TimeoutException)
                    {
                        // Timeout is acceptable
                    }

                _interceptor.Dispose();
            }
            catch (Exception ex)
            {
                var error = $"Error stopping interceptor: {ex.Message}";
                _errors.Add(error);
                ErrorOccurred?.Invoke(error);
            }
            finally
            {
                _interceptor = null;
                _interceptorTask = null;
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;

                Status = new InterceptorStatus { IsRunning = false };
                StatusChanged?.Invoke();
            }
    }

    public void ClearSessions()
    {
        _sessions.Clear();
        _amfTraffic.Clear();
        Status.TotalSessions = 0;
        Status.ActiveSessions = 0;
        TrafficUpdated?.Invoke();
        StatusChanged?.Invoke();
    }

    public TrafficSession? GetSession(string sessionId)
    {
        _sessions.TryGetValue(sessionId, out var session);
        return session;
    }

    private Task OnTransactionCompleted(object? sender, HttpTransactionCompletedEventArgs e)
    {
        try
        {
            var transaction = e.Transaction;

            // Create or update Charles session from the completed transaction
            var session = new TrafficSession
            {
                Id = transaction.TransactionId,
                Hostname = transaction.Hostname,
                Port = transaction.Port,
                Protocol = transaction.Protocol,
                StartTime = transaction.StartTime,
                EndTime = transaction.EndTime
            };

            // Map request data
            if (transaction.Request != null)
            {
                session.Request = new HttpRequestData
                {
                    Method = transaction.Request.Method ?? "GET",
                    Uri = transaction.Request.Uri,
                    ContentType = transaction.Request.ContentType,
                    Content = transaction.Request.Content,
                    Protocol = transaction.Protocol,
                    Timestamp = transaction.StartTime,
                    Headers = new Dictionary<string, string>()
                };

                // Map request headers
                if (transaction.Request.Headers?.Any() == true)
                    foreach (var header in transaction.Request.Headers)
                        if (header.Value?.Any() == true)
                            session.Request.Headers[header.Key] = string.Join(", ", header.Value);
            }

            // Map response data
            if (transaction.Response != null)
            {
                session.Response = new HttpResponseData
                {
                    StatusCode = transaction.Response.StatusCode,
                    ReasonPhrase =
                        transaction.Response.ReasonPhrase ?? GetReasonPhrase(transaction.Response.StatusCode),
                    ContentType = transaction.Response.ContentType,
                    Content = transaction.Response.Content,
                    Timestamp = transaction.EndTime ?? DateTime.Now,
                    Duration = transaction.Duration,
                    Headers = new Dictionary<string, string>()
                };

                // Map response headers
                if (transaction.Response.Headers?.Any() == true)
                    foreach (var header in transaction.Response.Headers)
                        if (header.Value?.Any() == true)
                            session.Response.Headers[header.Key] = string.Join(", ", header.Value);
            }

            // Process AMF data if available
            ProcessAmfData(transaction, session);

            // Store session
            _sessions[session.Id] = session;

            // Update status counters
            Status.TotalSessions = _sessions.Count;
            Status.ActiveSessions = _sessions.Values.Count(s => !s.IsComplete);

            // Notify listeners
            Task.Run(() =>
            {
                try
                {
                    NewSession?.Invoke(session);
                    TrafficUpdated?.Invoke();
                    StatusChanged?.Invoke();
                }
                catch (Exception ex)
                {
                    var error = $"Error notifying session update: {ex.Message}";
                    _errors.Add(error);
                    ErrorOccurred?.Invoke(error);
                }
            });
        }
        catch (Exception ex)
        {
            var error = $"Error processing completed transaction: {ex.Message}";
            _errors.Add(error);
            Task.Run(() => ErrorOccurred?.Invoke(error));
        }

        return Task.CompletedTask;
    }

    private Task OnTransactionTimedOut(object? sender, HttpTransactionTimedOutEventArgs e)
    {
        try
        {
            var transaction = e.Transaction;

            // Create a timed-out session
            var session = new TrafficSession
            {
                Id = transaction.TransactionId,
                Hostname = transaction.Hostname,
                Port = transaction.Port,
                Protocol = transaction.Protocol,
                StartTime = transaction.StartTime,
                IsTimedOut = true
            };

            // Map request data if available
            if (transaction.Request != null)
            {
                session.Request = new HttpRequestData
                {
                    Method = transaction.Request.Method ?? "GET",
                    Uri = transaction.Request.Uri,
                    ContentType = transaction.Request.ContentType,
                    Content = transaction.Request.Content,
                    Protocol = transaction.Protocol,
                    Timestamp = transaction.StartTime,
                    Headers = new Dictionary<string, string>()
                };

                if (transaction.Request.Headers?.Any() == true)
                    foreach (var header in transaction.Request.Headers)
                        if (header.Value?.Any() == true)
                            session.Request.Headers[header.Key] = string.Join(", ", header.Value);
            }

            // Store timed-out session
            _sessions[session.Id] = session;

            // Update status counters
            Status.TotalSessions = _sessions.Count;
            Status.ActiveSessions = _sessions.Values.Count(s => !s.IsComplete);

            // Notify listeners
            Task.Run(() =>
            {
                try
                {
                    NewSession?.Invoke(session);
                    TrafficUpdated?.Invoke();
                    StatusChanged?.Invoke();
                }
                catch (Exception ex)
                {
                    var error = $"Error notifying timed-out session: {ex.Message}";
                    _errors.Add(error);
                    ErrorOccurred?.Invoke(error);
                }
            });
        }
        catch (Exception ex)
        {
            var error = $"Error processing timed-out transaction: {ex.Message}";
            _errors.Add(error);
            Task.Run(() => ErrorOccurred?.Invoke(error));
        }


        return Task.CompletedTask;
    }

    private void ProcessAmfData(HttpTransaction transaction, TrafficSession session)
    {
        // Process AMF request data
        if (transaction.Request?.IsAmf == true && transaction.Request.Amf?.DecodedContent != null)
        {
            var amfData = new AmfTrafficData
            {
                Hostname = transaction.Hostname,
                Port = transaction.Port,
                Protocol = transaction.Protocol,
                Direction = "Request",
                Uri = transaction.Request.Uri?.ToString() ?? session.DisplayName,
                ContentType = transaction.Request.ContentType ?? "application/x-amf",
                Timestamp = transaction.StartTime,
                Method = transaction.Request.Method,
                StatusCode = 0,
                DecodedContent = transaction.Request.Amf.DecodedContent.Content,
                Error = transaction.Request.Amf.Error,
                RawContent = transaction.Request.Content
            };

            _amfTraffic.Add(amfData);
            Task.Run(() => NewAmfTraffic?.Invoke(amfData));
        }

        // Process AMF response data
        if (transaction.Response?.IsAmf == true && transaction.Response.Amf?.DecodedContent != null)
        {
            var amfData = new AmfTrafficData
            {
                Hostname = transaction.Hostname,
                Port = transaction.Port,
                Protocol = transaction.Protocol,
                Direction = "Response",
                Uri = transaction.Request?.Uri?.ToString() ?? session.DisplayName,
                ContentType = transaction.Response.ContentType ?? "application/x-amf",
                Timestamp = transaction.EndTime ?? DateTime.Now,
                Method = transaction.Request?.Method ?? "GET",
                StatusCode = (int)transaction.Response.StatusCode,
                DecodedContent = transaction.Response.Amf.DecodedContent.Content,
                Error = transaction.Response.Amf.Error,
                RawContent = transaction.Response.Content
            };

            _amfTraffic.Add(amfData);
            Task.Run(() => NewAmfTraffic?.Invoke(amfData));
        }
    }

    private static string GetReasonPhrase(HttpStatusCode statusCode)
    {
        return statusCode switch
        {
            HttpStatusCode.OK => "OK",
            HttpStatusCode.NotFound => "Not Found",
            HttpStatusCode.InternalServerError => "Internal Server Error",
            HttpStatusCode.BadRequest => "Bad Request",
            HttpStatusCode.Unauthorized => "Unauthorized",
            HttpStatusCode.Forbidden => "Forbidden",
            HttpStatusCode.MovedPermanently => "Moved Permanently",
            HttpStatusCode.Found => "Found",
            _ => statusCode.ToString()
        };
    }

    private async Task OnCertificateGenerated(object? sender, CertificateGeneratedEventArgs e)
    {
        try
        {
            var certInfo = new CertificateInfo
            {
                Hostname = e.Hostname,
                Subject = e.CertificateSubject,
                Thumbprint = e.Thumbprint,
                ValidFrom = e.ValidFrom,
                ValidTo = e.ValidTo,
                GeneratedAt = DateTime.Now
            };

            _certificates.Add(certInfo);

            await Task.Run(() => CertificateGenerated?.Invoke(certInfo)).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            var error = $"Error processing certificate: {ex.Message}";
            _errors.Add(error);
            await Task.Run(() => ErrorOccurred?.Invoke(error)).ConfigureAwait(false);
        }
    }

    private async Task OnErrorOccurred(object? sender, InterceptorErrorEventArgs e)
    {
        var error = $"{e.Source}: {e.Message}";
        if (e.Exception != null) error += $" - {e.Exception.GetType().Name}: {e.Exception.Message}";

        _errors.Add(error);
        Status.LastError = error;

        await Task.Run(() =>
        {
            ErrorOccurred?.Invoke(error);
            StatusChanged?.Invoke();
        }).ConfigureAwait(false);
    }
}