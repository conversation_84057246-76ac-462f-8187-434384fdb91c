using Dolo.Pluto.Shard.Services;
using Dolo.Pluto.Tool.Charles.Models;

namespace Dolo.Pluto.Tool.Charles.Services;

public class ViewStateService : IService
{
    private ViewType _activeView = ViewType.Structure;

    public ViewType ActiveView
    {
        get => _activeView;
        private set
        {
            if (_activeView != value)
            {
                _activeView = value;
                ViewChanged?.Invoke();
            }
        }
    }

    public bool IsStructureView => ActiveView == ViewType.Structure;
    public bool IsSequenceView => ActiveView == ViewType.Sequence;
    public event Action? ViewChanged;
    public event Action? ExpandAllRequested;
    public event Action? CollapseAllRequested;

    public void SetView(ViewType viewType)
    {
        ActiveView = viewType;
    }

    public void RequestExpandAll()
    {
        ExpandAllRequested?.Invoke();
    }

    public void RequestCollapseAll()
    {
        CollapseAllRequested?.Invoke();
    }

    private async Task NotifyViewChangedAsync()
    {
        await Task.Delay(50).ConfigureAwait(false);
        ViewChanged?.Invoke();
    }
}