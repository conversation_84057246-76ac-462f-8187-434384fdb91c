@page "/"
@using Dolo.Pluto.Shard.Components.Toast
@using Dolo.Pluto.Tool.Pixler.Components

<div class="flex flex-col h-screen bg-bg-base">
    <Header OnLiveExchangeToggle="@HandleLiveExchangeToggle"
            IsLiveExchangeActive="@IsLiveExchangeVisible"/>

    <div class="flex-1 flex overflow-hidden">
        <div class="flex-1 flex flex-col overflow-hidden">
            <div class="flex-1 overflow-y-auto">
                <div class="w-full max-w-4xl mx-auto p-6">
                    <AccountSetup @ref="AccountSetupRef"/>
                    <ItemSelection @ref="ItemSelectionRef"/>
                </div>
            </div>
        </div>
    </div>

    <!-- Live Exchange Component -->
    <LiveExchange @ref="LiveExchangeRef"
                  IsVisible="@IsLiveExchangeVisible"
                  CurrentItem="@GetCurrentExchangeItem()"
                  CurrentRound="@CurrentRound"
                  TotalRounds="@TotalRounds"
                  Progress="@ExchangeProgress"
                  SentCount="@SentCount"
                  ReceivedCount="@ReceivedCount"
                  AccountAHasItem="@AccountAHasItem"
                  AccountBHasItem="@AccountBHasItem"
                  AccountAStatus="@AccountAStatus"
                  AccountBStatus="@AccountBStatus"
                  IsExchangeActive="@IsExchangeActive"/>

    <!-- Toast Container -->
    <ToastContainer/>
</div>

