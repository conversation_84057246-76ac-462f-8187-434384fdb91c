using System.Diagnostics.CodeAnalysis;
using Dolo.Pluto.Shard.Components;
using Dolo.Pluto.Tool.Pixler.Services;
using Dolo.Pluto.Tool.Pixler.Components;
using Microsoft.AspNetCore.Components;
using Dolo.Planet;

namespace Dolo.Pluto.Tool.Pixler.Components.Content;

public partial class Main : ComponentBase {

    [Inject, AllowNull] public MainService MainServiceInstance { get; set; }

    public InitializationScreen? InitializationScreen;

    // Component references
    public AccountSetup? AccountSetupRef;
    public ItemSelection? ItemSelectionRef;
    public LiveExchange? LiveExchangeRef;
    public ExchangeErrorModal? ExchangeErrorModalRef;

    // Error modal state
    public bool IsErrorModalVisible = false;
    public ExchangeErrorType CurrentErrorType;
    public string CurrentErrorMessage = "";
    public string? CurrentAffectedAccount;

    // Exchange state (for LiveExchange component)
    public int ExchangeProgress = 0;

    // Live Exchange state
    public bool IsLiveExchangeVisible = false;
    public int CurrentRound = 3;
    public int TotalRounds = 10;
    public int SentCount = 3;
    public int ReceivedCount = 2;
    public bool AccountAHasItem = true;
    public bool AccountBHasItem = false;
    public string AccountAStatus = "Sending";
    public string AccountBStatus = "Waiting";
    public bool IsExchangeActive = false;

    public Task StateHasChangedAsync() => InvokeAsync(StateHasChanged);

    protected override Task OnAfterRenderAsync(bool firstRender) {
        if (!firstRender) return Task.CompletedTask;

        MainServiceInstance.Main = this;
        return Task.CompletedTask;
    }

    public LiveExchange.ExchangeItem? GetCurrentExchangeItem()
    {
        var selectedItem = ItemSelectionRef?.SelectedItem;
        if (selectedItem == null) return null;

        return new LiveExchange.ExchangeItem
        {
            Name = selectedItem.Name,
            DisplayName = selectedItem.DisplayName,
            ImagePath = selectedItem.ImagePath,
            ItemId = selectedItem.ItemId
        };
    }



    public void UpdateLiveExchangeProgress(int progress)
    {
        ExchangeProgress = progress;
        LiveExchangeRef?.UpdateProgress(progress);
        StateHasChanged();
    }

    public void UpdateTransferStats(int sent, int received)
    {
        SentCount = sent;
        ReceivedCount = received;
        LiveExchangeRef?.UpdateTransferStats(sent, received);
        StateHasChanged();
    }

    public void UpdateAccountStatus(string account, string status)
    {
        if (account == "A")
        {
            AccountAStatus = status;
        }
        else if (account == "B")
        {
            AccountBStatus = status;
        }
        LiveExchangeRef?.UpdateAccountStatus(account, status);
        StateHasChanged();
    }

    public async Task HandleLiveExchangeToggle()
    {
        IsLiveExchangeVisible = !IsLiveExchangeVisible;
        StateHasChanged();
        await Task.CompletedTask;
    }

    public bool AreAccountsConnected()
    {
        return AccountSetupRef?.AccountA.IsConnected == true &&
               AccountSetupRef?.AccountB.IsConnected == true;
    }

    public (MspClient? AccountA, MspClient? AccountB) GetConnectedClients()
    {
        return (AccountSetupRef?.AccountA.MspClient, AccountSetupRef?.AccountB.MspClient);
    }

    public async Task HandleExchangeError(ExchangeError error)
    {
        CurrentErrorType = error.Type;
        CurrentErrorMessage = error.Message;
        CurrentAffectedAccount = error.AffectedAccount;
        IsErrorModalVisible = true;
        StateHasChanged();
    }

    private async Task HandleErrorRetry()
    {
        IsErrorModalVisible = false;
        StateHasChanged();
        // The modal is hidden, user can select a different item or try again
    }

    private async Task HandleErrorLogout()
    {
        IsErrorModalVisible = false;

        if (!string.IsNullOrEmpty(CurrentAffectedAccount) && AccountSetupRef != null)
        {
            await AccountSetupRef.LogoutAccountAsync(CurrentAffectedAccount);
        }

        StateHasChanged();
    }

    private async Task HandleErrorClose()
    {
        IsErrorModalVisible = false;
        StateHasChanged();
    }
}
