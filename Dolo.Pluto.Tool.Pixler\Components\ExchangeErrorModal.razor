@using Dolo.Pluto.Tool.Pixler.Services

<div class="@GetModalClasses()" @onclick="CloseOnOutsideClick">
    <div @onclick:stopPropagation="true" class="relative bg-bg-surface border border-border-l1 rounded-xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300 ease-out">
        <!-- Header -->
        <div class="flex items-center justify-between p-6 border-b border-border-l1">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-text-main">Exchange Error</h3>
                    <p class="text-sm text-text-secondary">@GetErrorTypeDescription()</p>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="p-6">
            <div class="mb-4">
                <p class="text-text-main">@ErrorMessage</p>
                
                @if (!string.IsNullOrEmpty(AffectedAccount))
                {
                    <div class="mt-3 p-3 bg-bg-surface-hover rounded-lg border border-border-l1">
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 rounded-full bg-red-500"></div>
                            <span class="text-sm font-medium text-text-main">Affected Account: @AffectedAccount</span>
                        </div>
                    </div>
                }
            </div>

            @if (ErrorType == ExchangeErrorType.GiftLimitReached)
            {
                <div class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="flex items-start space-x-2">
                        <svg class="w-5 h-5 text-yellow-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-yellow-800">Daily Gift Limit Reached</p>
                            <p class="text-sm text-yellow-700 mt-1">The account has reached its daily gift limit. You can either logout this account to use a different one, or retry the exchange later.</p>
                        </div>
                    </div>
                </div>
            }
            else if (ErrorType == ExchangeErrorType.ItemNotFound)
            {
                <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-start space-x-2">
                        <svg class="w-5 h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-blue-800">Item Not Available</p>
                            <p class="text-sm text-blue-700 mt-1">Neither account has this item in their inventory. Please select a different item to exchange.</p>
                        </div>
                    </div>
                </div>
            }
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-3 p-6 pt-0">
            <button @onclick="OnRetry" 
                    class="px-4 py-2 text-sm font-medium text-text-secondary hover:text-text-main bg-bg-surface-hover hover:bg-bg-surface border border-border-l1 hover:border-border-l2 rounded-lg transition-all duration-200">
                Retry
            </button>
            
            @if (ShowLogoutButton)
            {
                <button @onclick="OnLogout" 
                        class="px-4 py-2 text-sm font-medium text-white bg-red-500 hover:bg-red-600 rounded-lg transition-all duration-200 shadow-sm">
                    Logout Account
                </button>
            }
            else
            {
                <button @onclick="OnClose" 
                        class="px-4 py-2 text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 rounded-lg transition-all duration-200 shadow-sm">
                    Close
                </button>
            }
        </div>
    </div>
</div>

@code {
    [Parameter] public bool IsVisible { get; set; }
    [Parameter] public ExchangeErrorType ErrorType { get; set; }
    [Parameter] public string ErrorMessage { get; set; } = "";
    [Parameter] public string? AffectedAccount { get; set; }
    [Parameter] public EventCallback OnRetry { get; set; }
    [Parameter] public EventCallback OnLogout { get; set; }
    [Parameter] public EventCallback OnClose { get; set; }

    private bool ShowLogoutButton => ErrorType == ExchangeErrorType.GiftLimitReached && !string.IsNullOrEmpty(AffectedAccount);

    private string GetModalClasses()
    {
        var baseClasses = "fixed inset-0 z-[9999] flex items-center justify-center bg-black/50 backdrop-blur-sm transition-all duration-300";
        
        if (IsVisible)
        {
            return baseClasses + " opacity-100";
        }
        
        return baseClasses + " opacity-0 pointer-events-none";
    }

    private string GetErrorTypeDescription()
    {
        return ErrorType switch
        {
            ExchangeErrorType.GiftLimitReached => "Daily gift limit exceeded",
            ExchangeErrorType.ItemNotFound => "Item not available",
            ExchangeErrorType.GiftFailed => "Gift transfer failed",
            ExchangeErrorType.NetworkError => "Network connection error",
            _ => "Unknown error occurred"
        };
    }

    private async Task CloseOnOutsideClick()
    {
        await OnClose.InvokeAsync();
    }
}
