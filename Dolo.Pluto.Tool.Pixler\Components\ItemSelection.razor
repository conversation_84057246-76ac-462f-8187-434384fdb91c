<!-- Item Selection -->
<div class="mt-2">
    <div class="flex items-center mb-2">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1.5 text-text-main" viewBox="0 0 20 20" fill="currentColor">
            <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
        </svg>
        <h3 class="text-xs font-bold text-text-main font-jakarta">Item Selection</h3>
    </div>

    <div class="bg-bg-surface border border-border-l1 rounded-xl shadow-sm overflow-hidden">
        <!-- Inventory Grid Section -->
        <div class="p-4">
            <div class="flex gap-2 overflow-x-auto pb-2 pt-1 pr-1" style="scrollbar-width: thin;">
                @foreach (var item in Items)
                {
                    <div @onclick="@(() => SelectItem(item))"
                         class="@GetItemClasses(item) group rounded-xl p-3 flex flex-col items-center cursor-pointer transition-all duration-200 min-w-[70px] shadow-sm">
                        @if (SelectedItem?.Name == item.Name)
                        {
                            <!-- Selected Checkmark -->
                            <div class="absolute -top-1 -right-1 w-4 h-4 bg-border-focus rounded-full flex items-center justify-center shadow-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-2.5 w-2.5 text-white" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </div>
                        }
                        <div class="w-8 h-8 bg-white/80 backdrop-blur-sm rounded-lg flex items-center justify-center mb-2 shadow-sm group-hover:shadow-md transition-all duration-300">
                            @if (!string.IsNullOrEmpty(item.ImagePath))
                            {
                                <img src="@item.ImagePath" alt="@item.Name" class="w-6 h-6 object-contain" />
                            }
                            else
                            {
                                <span class="text-sm">@item.Icon</span>
                            }
                        </div>
                        <span class="text-[10px] @(SelectedItem?.Name == item.Name ? "text-text-main" : "text-text-secondary group-hover:text-text-main") text-center leading-tight font-medium transition-colors duration-300">@item.DisplayName</span>
                    </div>
                }
            </div>

            @if (SelectedItem != null)
            {
                <!-- Selected Item Display & Start Button -->
                <div class="flex items-center gap-3 pt-2 border-t border-border-strong">
                    <div class="flex items-center flex-1">
                        <div class="w-8 h-8 bg-bg-surface border border-primary rounded-lg flex items-center justify-center mr-2">
                            @if (!string.IsNullOrEmpty(SelectedItem.ImagePath))
                            {
                                <img src="@SelectedItem.ImagePath" alt="@SelectedItem.Name" class="w-5 h-5 object-contain" />
                            }
                            else
                            {
                                <span class="text-sm">@SelectedItem.Icon</span>
                            }
                        </div>
                        <div>
                            <div class="text-xs font-medium text-text-main">@SelectedItem.Name</div>
                            <div class="text-xs text-text-secondary">Selected for exchange</div>
                        </div>
                    </div>
                    <button @onclick="StartExchange" disabled="@IsExchangeInProgress"
                            class="bg-bg-surface-hover hover:brightness-110 text-text-main px-4 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 flex items-center border border-border-l1 hover:border-border-l2 shadow-sm hover:shadow">
                        @if (IsExchangeInProgress)
                        {
                            <svg class="animate-spin h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span>@ExchangeProgress%</span>
                        }
                        else
                        {
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span>Start Exchange</span>
                        }
                    </button>
                </div>
            }

        </div>
    </div>
</div>
