using Microsoft.AspNetCore.Components;

namespace Dolo.Pluto.Tool.Pixler.Components;

public partial class ItemSelection : ComponentBase, IDisposable
{
    public class GameItem
    {
        public string Name { get; set; } = "";
        public string Icon { get; set; } = "";
        public string ImagePath { get; set; } = "";
        public string DisplayName { get; set; } = "";
    }

    public List<GameItem> Items { get; set; } = new()
    {
        new() { Name = "Pixel Top Girl", Icon = "👕", ImagePath = "assets/Pixel_Top_Girl.png", DisplayName = "Top Girl" },
        new() { Name = "Pixel Top Boy", Icon = "👕", ImagePath = "assets/Pixel_Top_Boy.png", DisplayName = "Top Boy" },
        new() { Name = "Pixel Pants Boy", Icon = "🩳", ImagePath = "assets/Pixel_Pants_Boy.png", DisplayName = "Pants Boy" },
        new() { Name = "Pixel Skirt Girl", Icon = "👗", ImagePath = "assets/Pixel_Skirt_Girl.png", DisplayName = "Skirt Girl" },
        new() { Name = "Pixel Cap Boy", Icon = "🧢", ImagePath = "assets/Pixel_Cap_Boy.png", DisplayName = "Cap Boy" },
        new() { Name = "Pixel Hair Girl", Icon = "💇‍♀️", ImagePath = "assets/Pixel_Hair_Girl.png", DisplayName = "Hair Girl" },
        new() { Name = "Pixel Hair Boy", Icon = "💇‍♂️", ImagePath = "assets/Pixel_Hair_Boy.png", DisplayName = "Hair Boy" },
        new() { Name = "Pixel Bow Girl", Icon = "🎀", ImagePath = "assets/Pixel_Bow_Girl.png", DisplayName = "Bow Girl" }
    };

    public GameItem? SelectedItem { get; set; }

    protected override void OnInitialized()
    {
        // Set Pixel Top Girl as default selected item to match HTML
        SelectedItem = Items.FirstOrDefault(i => i.Name == "Pixel Top Girl");
    }

    private string GetItemClasses(GameItem item)
    {
        var baseClasses = "relative bg-bg-surface border border-border-l1 hover:bg-bg-surface-hover hover:border-border-l2";

        if (SelectedItem?.Name == item.Name)
        {
            return baseClasses + " bg-bg-surface-hover border-border-focus";
        }

        return baseClasses;
    }

    private void LoadItems()
    {
        // Simulate loading items - in real implementation this would load from game
        StateHasChanged();
    }

    private void SelectItem(GameItem item)
    {
        SelectedItem = item;
        StateHasChanged();
    }

    public bool IsExchangeInProgress { get; set; } = false;
    public string ExchangeStatus { get; set; } = "";
    public int ExchangeProgress { get; set; } = 0;



    private async Task StartExchange()
    {
        if (SelectedItem == null || IsExchangeInProgress) return;

        IsExchangeInProgress = true;
        ExchangeStatus = "Initializing exchange...";
        ExchangeProgress = 0;
        StateHasChanged();

        // Simulate exchange progress
        await SimulateExchangeProgress();
    }

    private async Task SimulateExchangeProgress()
    {
        var stages = new[]
        {
            ("Connecting to server...", 10),
            ("Validating accounts...", 25),
            ("Preparing item transfer...", 40),
            ("Transferring item...", 70),
            ("Confirming receipt...", 90),
            ("Exchange completed!", 100)
        };

        foreach (var (status, progress) in stages)
        {
            ExchangeStatus = status;
            ExchangeProgress = progress;
            StateHasChanged();

            // Simulate processing time
            await Task.Delay(1500);
        }

        // Reset after completion
        await Task.Delay(2000);
        IsExchangeInProgress = false;
        ExchangeStatus = "";
        ExchangeProgress = 0;
        StateHasChanged();
    }

    public void Dispose()
    {
        // No resources to dispose currently
    }
}
