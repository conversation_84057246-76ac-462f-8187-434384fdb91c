using Microsoft.AspNetCore.Components;

namespace Dolo.Pluto.Tool.Pixler.Components;

public partial class LiveExchange : ComponentBase, IDisposable
{
    public class ExchangeItem
    {
        public string Name { get; set; } = "";
        public string DisplayName { get; set; } = "";
        public string ImagePath { get; set; } = "";
    }

    [Parameter] public bool IsVisible { get; set; } = true;
    [Parameter] public ExchangeItem? CurrentItem { get; set; }
    [Parameter] public int CurrentRound { get; set; } = 3;
    [Parameter] public int TotalRounds { get; set; } = 10;
    [Parameter] public int Progress { get; set; } = 30;
    [Parameter] public int SentCount { get; set; } = 3;
    [Parameter] public int ReceivedCount { get; set; } = 2;
    [Parameter] public bool AccountAHasItem { get; set; } = true;
    [Parameter] public bool AccountBHasItem { get; set; } = false;
    [Parameter] public string AccountAStatus { get; set; } = "Sending";
    [Parameter] public string AccountBStatus { get; set; } = "Waiting";
    [Parameter] public bool IsExchangeActive { get; set; } = false;

    protected override void OnInitialized()
    {
        // Set default item if none provided
        CurrentItem ??= new ExchangeItem
        {
            Name = "Pixel Top Girl",
            DisplayName = "Top Girl",
            ImagePath = "assets/Pixel_Top_Girl.png"
        };
    }

    private string GetExchangeClasses()
    {
        var baseClasses = "fixed bottom-4 left-4 w-64 bg-bg-surface border border-border-l1 rounded-lg shadow-lg z-50 transition-all duration-500 ease-in-out";

        if (!IsVisible)
        {
            return baseClasses + " transform translate-y-full opacity-0 scale-95";
        }

        return baseClasses + " transform translate-y-0 opacity-100 scale-100";
    }

    private void ToggleVisibility()
    {
        IsVisible = !IsVisible;
        StateHasChanged();
    }

    private string GetAccountStatus(string account)
    {
        return account switch
        {
            "A" => AccountAStatus,
            "B" => AccountBStatus,
            _ => "Unknown"
        };
    }

    private string GetAccountStatusColor(string account)
    {
        var status = GetAccountStatus(account);
        return status switch
        {
            "Sending" => "bg-warning",
            "Waiting" => "bg-text-secondary",
            "Receiving" => "bg-success",
            "Complete" => "bg-success",
            _ => "bg-text-secondary"
        };
    }

    private string GetAccountStatusTextColor(string account)
    {
        var status = GetAccountStatus(account);
        return status switch
        {
            "Sending" => "text-warning",
            "Waiting" => "text-text-secondary",
            "Receiving" => "text-success",
            "Complete" => "text-success",
            _ => "text-text-secondary"
        };
    }

    private string GetAccountBorderColor(string account)
    {
        var status = GetAccountStatus(account);
        return status switch
        {
            "Sending" => "border-warning/30",
            "Receiving" => "border-success/30",
            "Complete" => "border-success/30",
            _ => "border-border-l1"
        };
    }

    public void UpdateProgress(int newProgress)
    {
        Progress = Math.Clamp(newProgress, 0, 100);
        StateHasChanged();
    }

    public void UpdateTransferStats(int sent, int received)
    {
        SentCount = sent;
        ReceivedCount = received;
        StateHasChanged();
    }

    public void UpdateAccountStatus(string account, string status)
    {
        if (account == "A")
        {
            AccountAStatus = status;
        }
        else if (account == "B")
        {
            AccountBStatus = status;
        }
        StateHasChanged();
    }

    public void UpdateItemLocation(bool accountAHas, bool accountBHas)
    {
        AccountAHasItem = accountAHas;
        AccountBHasItem = accountBHas;
        StateHasChanged();
    }

    public void Dispose()
    {
        // No resources to dispose currently
    }
}
