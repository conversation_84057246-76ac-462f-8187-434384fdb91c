using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.Tool.Pixler.Services;

public class ExchangeService : IService
{
    public ExchangeState CurrentExchange { get; private set; } = new();
    public event Action<ExchangeState>? ExchangeStateChanged;

    public async Task StartExchange(string itemName, string itemIcon)
    {
        if (CurrentExchange.IsActive) return;

        CurrentExchange = new ExchangeState
        {
            IsActive = true,
            ItemName = itemName,
            ItemIcon = itemIcon,
            Progress = 0,
            Status = "Initializing exchange..."
        };

        ExchangeStateChanged?.Invoke(CurrentExchange);

        // Simulate exchange progress
        await SimulateExchangeProgress();
    }

    public void CancelExchange()
    {
        CurrentExchange = new ExchangeState();
        ExchangeStateChanged?.Invoke(CurrentExchange);
    }

    public void PauseExchange()
    {
        CurrentExchange.IsPaused = !CurrentExchange.IsPaused;
        ExchangeStateChanged?.Invoke(CurrentExchange);
    }

    private async Task SimulateExchangeProgress()
    {
        var stages = new[]
        {
            ("Connecting to server...", 10),
            ("Validating accounts...", 25),
            ("Preparing item transfer...", 40),
            ("Transferring item...", 70),
            ("Confirming receipt...", 90),
            ("Exchange completed!", 100)
        };

        foreach (var (status, progress) in stages)
        {
            if (!CurrentExchange.IsActive) break;

            // Wait if paused
            while (CurrentExchange.IsPaused && CurrentExchange.IsActive) await Task.Delay(100);

            if (!CurrentExchange.IsActive) break;

            CurrentExchange.Status = status;
            CurrentExchange.Progress = progress;
            ExchangeStateChanged?.Invoke(CurrentExchange);

            // Simulate processing time
            await Task.Delay(1500);
        }

        // Reset after completion
        if (CurrentExchange.IsActive)
        {
            await Task.Delay(2000);
            CurrentExchange = new ExchangeState();
            ExchangeStateChanged?.Invoke(CurrentExchange);
        }
    }
}

public class ExchangeState
{
    public bool IsActive { get; set; }
    public string ItemName { get; set; } = "";
    public string ItemIcon { get; set; } = "";
    public string Status { get; set; } = "";
    public int Progress { get; set; }
    public bool IsPaused { get; set; }
}