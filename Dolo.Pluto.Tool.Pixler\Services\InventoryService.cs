using Dolo.Pluto.Shard.Services;
using Dolo.Planet;
using Dolo.Pluto.Tool.Pixler.Components;

namespace Dolo.Pluto.Tool.Pixler.Services;

public class InventoryService : IService
{
    public async Task<InventoryValidationResult> ValidateItemAvailabilityAsync(MspClient accountA, MspClient accountB, ulong itemId, string itemName)
    {
        try
        {
            var accountAItems = await LoadInventoryAsync(accountA, "Account A");
            var accountBItems = await LoadInventoryAsync(accountB, "Account B");

            if (!accountAItems.Success)
            {
                return InventoryValidationResult.Failure($"Failed to load Account A inventory: {accountAItems.ErrorMessage}");
            }

            if (!accountBItems.Success)
            {
                return InventoryValidationResult.Failure($"Failed to load Account B inventory: {accountBItems.ErrorMessage}");
            }

            var accountAHasItem = accountAItems.Items.Any(item => item.Cloth?.Id == itemId);
            var accountBHasItem = accountBItems.Items.Any(item => item.Cloth?.Id == itemId);

            if (!accountAHasItem && !accountBHasItem)
            {
                return InventoryValidationResult.Failure($"Neither account has the item '{itemName}' in their inventory. Please select a different item or ensure the item is available in at least one account.");
            }

            return new InventoryValidationResult
            {
                Success = true,
                AccountAHasItem = accountAHasItem,
                AccountBHasItem = accountBHasItem,
                ItemName = itemName,
                ItemId = itemId,
                AccountAInventoryCount = accountAItems.Items.Count,
                AccountBInventoryCount = accountBItems.Items.Count
            };
        }
        catch (Exception ex)
        {
            return InventoryValidationResult.Failure($"Error validating inventory: {ex.Message}");
        }
    }

    public async Task<InventoryLoadResult> LoadInventoryAsync(MspClient client, string accountName)
    {
        try
        {
            var items = await client.LoadPagedActorGiftableItems();
            
            return new InventoryLoadResult
            {
                Success = true,
                Items = items.ToList(),
                AccountName = accountName,
                LoadedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            return new InventoryLoadResult
            {
                Success = false,
                ErrorMessage = ex.Message,
                AccountName = accountName,
                LoadedAt = DateTime.UtcNow
            };
        }
    }

    public async Task<List<GameItem>> GetAvailablePixelItemsAsync(MspClient accountA, MspClient accountB, List<GameItem> allPixelItems)
    {
        var availableItems = new List<GameItem>();

        try
        {
            var accountAItems = await LoadInventoryAsync(accountA, "Account A");
            var accountBItems = await LoadInventoryAsync(accountB, "Account B");

            if (!accountAItems.Success || !accountBItems.Success)
            {
                // If we can't load inventory, return all items (let validation handle the error later)
                return allPixelItems;
            }

            var allInventoryItems = accountAItems.Items.Concat(accountBItems.Items).ToList();

            foreach (var pixelItem in allPixelItems)
            {
                var hasItem = allInventoryItems.Any(invItem => invItem.Cloth?.Id == pixelItem.ItemId);
                if (hasItem)
                {
                    availableItems.Add(pixelItem);
                }
            }

            return availableItems;
        }
        catch
        {
            // If there's an error, return all items to avoid breaking the UI
            return allPixelItems;
        }
    }

    public async Task<ItemSearchResult> FindItemInInventoriesAsync(MspClient accountA, MspClient accountB, string itemName)
    {
        try
        {
            var accountAItems = await LoadInventoryAsync(accountA, "Account A");
            var accountBItems = await LoadInventoryAsync(accountB, "Account B");

            var result = new ItemSearchResult
            {
                Success = true,
                ItemName = itemName
            };

            if (accountAItems.Success)
            {
                var foundInA = accountAItems.Items.FirstOrDefault(item => 
                    item.Cloth?.Name?.Contains(itemName, StringComparison.OrdinalIgnoreCase) == true);
                
                if (foundInA != null)
                {
                    result.FoundInAccountA = true;
                    result.AccountAItemId = foundInA.Cloth?.Id ?? 0;
                    result.AccountAItemName = foundInA.Cloth?.Name ?? "";
                }
            }

            if (accountBItems.Success)
            {
                var foundInB = accountBItems.Items.FirstOrDefault(item => 
                    item.Cloth?.Name?.Contains(itemName, StringComparison.OrdinalIgnoreCase) == true);
                
                if (foundInB != null)
                {
                    result.FoundInAccountB = true;
                    result.AccountBItemId = foundInB.Cloth?.Id ?? 0;
                    result.AccountBItemName = foundInB.Cloth?.Name ?? "";
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            return new ItemSearchResult
            {
                Success = false,
                ErrorMessage = ex.Message,
                ItemName = itemName
            };
        }
    }
}

// Supporting classes
public class InventoryValidationResult
{
    public bool Success { get; set; }
    public string ErrorMessage { get; set; } = "";
    public bool AccountAHasItem { get; set; }
    public bool AccountBHasItem { get; set; }
    public string ItemName { get; set; } = "";
    public ulong ItemId { get; set; }
    public int AccountAInventoryCount { get; set; }
    public int AccountBInventoryCount { get; set; }

    public static InventoryValidationResult Failure(string errorMessage) => new() { Success = false, ErrorMessage = errorMessage };
}

public class InventoryLoadResult
{
    public bool Success { get; set; }
    public string ErrorMessage { get; set; } = "";
    public List<Planet.Entities.Cloth.MspClothRel> Items { get; set; } = new();
    public string AccountName { get; set; } = "";
    public DateTime LoadedAt { get; set; }
}

public class ItemSearchResult
{
    public bool Success { get; set; }
    public string ErrorMessage { get; set; } = "";
    public string ItemName { get; set; } = "";
    public bool FoundInAccountA { get; set; }
    public bool FoundInAccountB { get; set; }
    public ulong AccountAItemId { get; set; }
    public ulong AccountBItemId { get; set; }
    public string AccountAItemName { get; set; } = "";
    public string AccountBItemName { get; set; } = "";
}
