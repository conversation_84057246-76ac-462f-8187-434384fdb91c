using Dolo.Pluto.Shard.Services;
using Dolo.Pluto.Shard.Toolbox;

namespace Dolo.Pluto.Web.Apps.Pluto;

public class AppService(PlatformService platformService) : IService
{
    /// <summary>
    ///     Toolbox object
    /// </summary>
    public Toolbox? Toolbox { get; set; }

    /// <summary>
    ///     Get the current version of the application
    ///     the application is our toolbox
    /// </summary>
    public string Version => platformService.CurrentPlatform switch
    {
        PlatformService.Platform.Windows => Toolbox?.Windows.Version ?? "Unknown",
        PlatformService.Platform.Mac => Toolbox?.Mac.Version ?? "Unknown",
        _ => "Unknown"
    };

    public string? DownloadUrl => platformService.CurrentPlatform switch
    {
        PlatformService.Platform.Windows => Toolbox?.Windows.DownloadUrl,
        PlatformService.Platform.Mac => Toolbox?.Mac.DownloadUrl,
        _ => "Unknown"
    };

    public bool IsMaintenanceMode => platformService.CurrentPlatform switch
    {
        PlatformService.Platform.Windows => Toolbox?.Windows.Maintenance.Enabled ?? false,
        PlatformService.Platform.Mac => Toolbox?.Mac.Maintenance.Enabled ?? false,
        _ => false
    };
}