using Dolo.Pluto.Shard.Services;
using Dolo.Pluto.Web.Hub.OAuth;
using Microsoft.JSInterop;
using Newtonsoft.Json;

namespace Dolo.Pluto.Web.Apps.Pluto.Auth;

public class AuthService(
    IJSRuntime jSRuntime,
    OAuthService oAuthService) : IService
{
    public Action<OAuthResult>? OnAuthUpdated { get; set; }

    public async Task PostMessageAsync(OAuthResult? oAuthResult)
    {
        await Task.Delay(TimeSpan.FromSeconds(1));
        await jSRuntime.InvokeVoidAsync("authService.postMessage",
            new
            {
                IsAuthenticated = oAuthResult?.IsAllowed ?? false,
                oAuthDataRaw = JsonConvert.SerializeObject(oAuthResult)
            });
    }


    public async Task<AuthServiceResult> TryAuthenticateAsync()
    {
        var authUrl = "/auth/discord/";
        var authRes = await jSRuntime.InvokeAsync<AuthServiceResult>("authService.openAuthPopup", authUrl);

        if (authRes.IsAuthenticated && !string.IsNullOrWhiteSpace(authRes.OAuthDataRaw))
        {
            var authData = JsonConvert.DeserializeObject<OAuthResult>(authRes.OAuthDataRaw, new JsonSerializerSettings
            {
                MissingMemberHandling = MissingMemberHandling.Ignore,
                Error = (sender, args) => { args.ErrorContext.Handled = true; }
            });

            // set the auth data and service
            authRes.OAuthData = authData;
            oAuthService.OAuthResult = authData;

            // invoke the event
            if (authData != null)
                OnAuthUpdated?.Invoke(authData);
        }

        return authRes;
    }
}