using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.Web.Apps.Pluto;

public class PlatformService : IService
{
    public enum Platform
    {
        Windows,
        Mac,
        Linux
    }

    public Platform CurrentPlatform => IsWindows ? Platform.Windows : IsMac ? Platform.Mac : Platform.Linux;
    public bool IsSupported => IsWindows || IsMac || IsLinux;
    public bool IsWindows { get; set; }
    public bool IsMac { get; set; }
    public bool IsLinux { get; set; }
}