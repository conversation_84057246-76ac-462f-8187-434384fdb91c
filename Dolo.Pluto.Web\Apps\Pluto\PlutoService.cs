﻿using System.Diagnostics.CodeAnalysis;
using Dolo.MovieStarPlanet.Components.Popup;
using Dolo.MovieStarPlanet.Components.Preloader;
using Dolo.Pluto.Shard.Services;
using Dolo.Pluto.Web.Apps.Pluto.Component;
using Dolo.Pluto.Web.Apps.Pluto.Content.Main.Navbar;
using Microsoft.AspNetCore.Components;

namespace Dolo.Pluto.Web.Apps.Pluto;

public class PlutoService(NavigationManager navigationManager, IHttpContextAccessor httpContextAccessor) : IService
{
    [AllowNull] public Content.Main.Pluto Pluto { get; set; }

    public Popup? Popup { get; set; }
    public Preloader? Preloader { get; set; }
    public UpdateNotificationPopup? UpdateNotificationPopup { get; set; }

    public async Task ShowPopupAsync(Action<PopupOptions> configure)
    {
        if (Popup is not null) await Popup.ShowPopupAsync(configure);
    }

    public void SetNavbarTo(NavbarTabType tabType)
    {
        Pluto.Navbar?.SetTab(tabType);
    }

    public void SignOut()
    {
        navigationManager.NavigateTo("/auth/discord/logout", true);
    }
}