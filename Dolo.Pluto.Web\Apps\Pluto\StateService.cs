using Dolo.Pluto.Shard.Services;

namespace Dolo.Pluto.Web.Apps.Pluto;

public class StateService(PlutoService plutoService, ToolService toolService) : IService
{
    public enum StateType
    {
        All,
        Pluto,
        Tools
    }

    public Action<StateType>? OnStateChanged;

    public void StateHasChanged(StateType stateType = StateType.All)
    {
        switch (stateType)
        {
            case StateType.Pluto:
                if (plutoService.Pluto != null) plutoService.Pluto.StateHasChangedAsync();
                break;
            case StateType.Tools:
                if (toolService.Tools != null) toolService.Tools.StateHasChangedAsync();
                break;
        }

        OnStateChanged?.Invoke(stateType);
    }
}