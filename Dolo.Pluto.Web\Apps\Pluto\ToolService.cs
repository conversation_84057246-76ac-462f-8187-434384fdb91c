using System.Diagnostics.CodeAnalysis;
using Dolo.Pluto.Shard.License;
using Dolo.Pluto.Shard.Services;
using Dolo.Pluto.Web.Apps.Pluto.Auth;
using Dolo.Pluto.Web.Apps.Pluto.Content.Main.Navbar;
using Dolo.Pluto.Web.Apps.Pluto.Content.Tooling;
using Dolo.Pluto.Web.Hub.OAuth;

namespace Dolo.Pluto.Web.Apps.Pluto;

/// <summary>
///     Represents a service that provides functionality related to tools in the application.
/// </summary>
public class ToolService : IService
{
    /// <summary>
    ///     Initializes a new instance of the <see cref="ToolService" /> class.
    /// </summary>
    /// <param name="oAuthService">The OAuth service instance.</param>
    /// <param name="authService">The authentication service instance.</param>
    public ToolService(OAuthService oAuthService, AuthService authService, PlutoService plutoService,
        AppService appService)
    {
        OAuthService = oAuthService;
        PlutoService = plutoService;
        AppService = appService;
        authService.OnAuthUpdated += oAuthResult => { OAuthService.OAuthResult = oAuthResult; };
    }

    [AllowNull] public Tools Tools { get; set; }

    /// <summary>
    ///     Gets or sets the OAuth service used for authentication.
    /// </summary>
    public OAuthService OAuthService { get; set; }

    /// <summary>
    ///     Gets or sets the Pluto service instance.
    /// </summary>
    public PlutoService PlutoService { get; set; }

    /// <summary>
    ///     Gets or sets the Pluto service instance.
    /// </summary>
    public AppService AppService { get; set; }


    /// <summary>
    ///     Determines whether the user has purchased a specific license permission.
    /// </summary>
    /// <param name="permission">The license permission to check.</param>
    /// <returns><c>true</c> if the user has the permission; otherwise, <c>false</c>.</returns>
    public bool HasPurchasedFor(LicensePermission permission)
    {
        return OAuthService.OAuthResult?.License?.User?.HasPermission(permission) ?? false;
    }

    /// <summary>
    ///     Gets the version information for a specific tool based on the license permission.
    /// </summary>
    /// <param name="permission">The license permission associated with the tool.</param>
    /// <returns>A string representing the version, or <c>null</c> if not available.</returns>
    public string? VersionFor(LicensePermission permission)
    {
        return permission switch
        {
            LicensePermission.Fame => "",
            LicensePermission.Autograph => "",
            _ => null
        };
    }


    /// <summary>
    ///     Sets the navbar to the toolbox tab.
    /// </summary>
    public void SetNavbarToToolbox()
    {
        PlutoService.SetNavbarTo(NavbarTabType.Tools);
        Tools.Navbar?.SetTab(Content.Tooling.Navbar.NavbarTabType.Toolbox);
    }
}