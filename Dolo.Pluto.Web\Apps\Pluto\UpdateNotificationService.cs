using Dolo.Pluto.Shard.Services;
using Microsoft.JSInterop;

namespace Dolo.Pluto.Web.Apps.Pluto;

/// <summary>
///     Service for managing update notifications with localStorage caching.
/// </summary>
public class UpdateNotificationService : IService
{
    /// <summary>
    ///     The current version of the application. Increment this when you want to show a new update notification.
    /// </summary>
    public const string CurrentVersion = "2.1.0";

    private readonly IJSRuntime _jsRuntime;

    public UpdateNotificationService(IJSRuntime jsRuntime)
    {
        _jsRuntime = jsRuntime;
    }

    /// <summary>
    ///     Checks if the user has seen the current version's update notification.
    /// </summary>
    /// <returns>True if the user has seen the notification, false otherwise.</returns>
    public async Task<bool> HasUserSeenCurrentUpdateAsync()
    {
        try
        {
            var seenVersion =
                await _jsRuntime.InvokeAsync<string?>("localStorage.getItem", "pluto-seen-update-version");
            return seenVersion == CurrentVersion;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    ///     Marks the current version as seen by the user.
    /// </summary>
    public async Task MarkCurrentUpdateAsSeenAsync()
    {
        try
        {
            await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "pluto-seen-update-version", CurrentVersion);
        }
        catch
        {
            // Silently fail if localStorage is not available
        }
    }

    /// <summary>
    ///     Gets the update notification details for the current version.
    /// </summary>
    /// <returns>The update notification information.</returns>
    public UpdateNotificationInfo GetCurrentUpdateInfo()
    {
        return new UpdateNotificationInfo
        {
            Version = CurrentVersion,
            Title = "🚀 Pluto Has Been Updated!",
            Message =
                "We've made some exciting improvements to enhance your experience! Check out the new features and improvements we've added.",
            Features = new List<string>
            {
                "✨ Enhanced user interface design",
                "⚡ Improved performance and speed",
                "🛠️ New tools and features",
                "🐛 Bug fixes and stability improvements"
            }
        };
    }
}

/// <summary>
///     Information about an update notification.
/// </summary>
public class UpdateNotificationInfo
{
    public string Version { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public List<string> Features { get; set; } = new();
}