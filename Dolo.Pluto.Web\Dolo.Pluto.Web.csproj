<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net10.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <_ContentIncludedByDefault Remove="Components\Layout\MainLayout.razor"/>
        <_ContentIncludedByDefault Remove="Pages\Counter.razor"/>
        <_ContentIncludedByDefault Remove="Pages\Error.razor"/>
        <_ContentIncludedByDefault Remove="Pages\Home.razor"/>
        <_ContentIncludedByDefault Remove="Pages\Weather.razor"/>
        <_ContentIncludedByDefault Remove="Layout\MainLayout.razor"/>
        <_ContentIncludedByDefault Remove="Layout\NavMenu.razor"/>
        <_ContentIncludedByDefault Remove="Components\Pages\Index.razor"/>
        <_ContentIncludedByDefault Remove="Content\Auth\Auth.razor"/>
        <_ContentIncludedByDefault Remove="Content\Auth\AuthCallback.razor"/>
        <_ContentIncludedByDefault Remove="Content\Tests\Test.razor"/>
        <_ContentIncludedByDefault Remove="Content\Pluto\Pluto.razor"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Dolo.Database\Dolo.Database.csproj"/>
        <ProjectReference Include="..\Dolo.MovieStarPlanet.Components\Dolo.MovieStarPlanet.Components.csproj"/>
        <ProjectReference Include="..\Dolo.Pluto.Components\Dolo.Pluto.Components.csproj"/>
        <ProjectReference Include="..\Dolo.Pluto.Shard\Dolo.Pluto.Shard.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="10.0.0-preview.6.25358.103"/>
        <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.13.0"/>
    </ItemGroup>

    <ItemGroup>
        <AdditionalFiles Include="Apps\Pluto\Auth\Auth.razor"/>
        <AdditionalFiles Include="Apps\Pluto\Component\AdvertisementStatusbar.razor"/>
        <AdditionalFiles Include="Apps\Pluto\Component\DesktopOnly.razor"/>
        <AdditionalFiles Include="Apps\Pluto\Component\FloatingElements.razor"/>
    </ItemGroup>

    <ItemGroup>
        <UpToDateCheckInput Remove="Components\Layout\MainLayout.razor"/>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Apps\"/>
        <Folder Include="Apps\Pluto\Content\Main\Tabs\App\"/>
        <Folder Include="Apps\Pluto\Content\Main\Tabs\App\Component\"/>
        <Folder Include="Apps\Pluto\Content\Tooling\Tabs\Feedback\"/>
    </ItemGroup>

</Project>
