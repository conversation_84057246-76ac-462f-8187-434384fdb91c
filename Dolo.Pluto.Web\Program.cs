using System.Text;
using System.Text.Json;
using Dolo.Pluto.Shard;
using Dolo.Pluto.Shard.Services;
using Dolo.Pluto.Web;
using Dolo.Pluto.Web.Apps.Pluto;
using Dolo.Pluto.Web.Hub;
using Dolo.Pluto.Web.Hub.Ticket;
using MessagePack;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OAuth;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.IdentityModel.Tokens;

// Add this using directive

// Register BsonSerializer
BsonRegister.Register();

// Create a web application builder
var builder = WebApplication.CreateBuilder(args);


builder.Services.AddLogging(logging =>
{
    logging.AddConsole();
    logging.AddDebug();
    logging.SetMinimumLevel(LogLevel.Debug); // Enable debug logging
});

// Scan and register scoped services
var serviceTypes = AppDomain.CurrentDomain.GetAssemblies()
    .SelectMany(s => s.GetTypes())
    .Where(p => typeof(IService).IsAssignableFrom(p) && p.IsClass);

foreach (var serviceType in serviceTypes)
    builder.Services.AddScoped(serviceType);

builder.Services.AddTransient<DownloadService>();

// Add core services
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents()
    .AddCircuitOptions(options => { options.DetailedErrors = true; });

builder.Services.AddControllers();
builder.Services.AddHttpContextAccessor();
builder.Services.AddSignalR(options =>
    {
        options.MaximumReceiveMessageSize = 102400000;
        options.EnableDetailedErrors = builder.Environment.IsDevelopment();
        options.MaximumParallelInvocationsPerClient = 5;
        options.StreamBufferCapacity = 20;
    })
    .AddMessagePackProtocol(options =>
    {
        options.SerializerOptions = MessagePackSerializerOptions.Standard
            .WithSecurity(MessagePackSecurity.UntrustedData)
            .WithCompression(MessagePackCompression.Lz4BlockArray);
    })
    .AddJsonProtocol(options =>
    {
        options.PayloadSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    });

// Add caching
builder.Services.AddOutputCache();
builder.Services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
    options.Providers.Add<BrotliCompressionProvider>();
    options.Providers.Add<GzipCompressionProvider>();
});
builder.Services.AddMemoryCache();

builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.Authority = "https://discord.com/api/oauth2/authorize";
        options.Audience = "https://discord.com/api/oauth2/token";
        options.RequireHttpsMetadata = false;
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidIssuer = "https://discord.com/api/oauth2/token",
            ValidateAudience = true,
            ValidAudience = "https://discord.com/api/oauth2/token",
            ValidateLifetime = true,
            IssuerSigningKey =
                new SymmetricSecurityKey(Encoding.UTF8.GetBytes("asIQW)=)�I=E)=)(=%)($=)%=)$(IRIRI=)$%($(/(((%/$(RU"))
        };
    }).AddCookie(a =>
    {
        a.CookieManager = new ChunkingCookieManager();
        a.Cookie.HttpOnly = true;
        a.Cookie.SameSite = SameSiteMode.None;
        a.Cookie.SecurePolicy = CookieSecurePolicy.Always;
    })
    // Consider using placeholder values for hiding client's sensitive data.
    .AddOAuth("Discord", a =>
    {
        a.ClientId = "729110700332417065";
        a.ClientSecret = "ZjosCX1GJujm-pUKc9hJZrDlLcBQYONU";
        a.CallbackPath = "/auth/discord/callback";
        a.AuthorizationEndpoint = "https://discord.com/api/oauth2/authorize";
        a.TokenEndpoint = "https://discord.com/api/oauth2/token";
        a.UserInformationEndpoint = "https://discord.com/api/users/@me";
        a.Scope.Add("identify");
        a.Scope.Add("guilds");
        a.Scope.Add("guilds.join");
        a.Scope.Add("guilds.members.read");
        a.ClaimActions.MapAll();
        a.Events = new OAuthEvents
        {
            OnCreatingTicket = async context => { await context.HandleAsync(); }
        };
    });
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("Discord", policy => policy.RequireAuthenticatedUser());
});

// Add health checks
builder.Services.AddHealthChecks();

// Build the application
var app = builder.Build();

app.Use(async (context, next) =>
{
    // Log basic request info
    if (context.Request.Method == "GET")
    {
        var endpoint = context.Request.Path;
        var queryString = context.Request.QueryString;
        var timestamp = DateTime.UtcNow;
        Console.WriteLine($"[{timestamp:yyyy-MM-dd HH:mm:ss}] GET Request: {endpoint}{queryString}");
    }

    // Ensure scheme is correctly identified, especially behind proxies
    if (context.Request.Headers.TryGetValue("X-Forwarded-Proto", out var proto)) context.Request.Scheme = proto;

    await next();
});

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}
else
{
    app.UseExceptionHandler("/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

// Use Forwarded Headers correctly - place it early in the pipeline
app.UseForwardedHeaders(new ForwardedHeadersOptions
{
    ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto
});

// HTTPS Redirection - place after Forwarded Headers
app.UseHttpsRedirection();

app.UseStaticFiles();

// Cookie Policy - place before UseAuthentication
app.UseCookiePolicy(new CookiePolicyOptions
{
    MinimumSameSitePolicy = SameSiteMode.None, // Necessary for OAuth external logins
    Secure = CookieSecurePolicy.Always // Requires HTTPS
});

app.UseRouting();

// Response Compression - place before components that can be compressed
app.UseResponseCompression();
app.UseOutputCache();

app.UseAuthentication(); // Must come before UseAuthorization
app.UseAuthorization();

app.UseAntiforgery(); // Place after UseAuthentication and UseAuthorization

app.MapControllers();
app.MapStaticAssets(); // Assuming this is a custom extension method
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();
app.MapHub<Pluto>("/b-hub");
app.MapHub<PlutoLog>("/log");
app.MapHub<PlutoUploadHub>("/upload");
app.MapHub<PlutoToolboxHub>("/toolbox");
app.MapHealthChecks("/health");

app.UseCors(policy =>
{
    policy.SetIsOriginAllowed(origin =>
            origin.StartsWith("http://127.0.0.1") || origin.StartsWith("http://localhost"))
        .AllowAnyMethod()
        .AllowAnyHeader();
});

app.Run();